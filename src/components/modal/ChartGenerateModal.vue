<template>
  <div>
    <el-dialog
      v-model="visible"
      title="AI生成图表"
      width="800px"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      center
    >
      <!-- <div class="space-y-4"> -->
      <el-form layout="vertical">
        <div class="reference-text" v-if="modalStore.referceText">
          <div class="text-ellipsis-wrapper">
            <div class="text-ellipsis">
              {{ modalStore.referceText }}
            </div>
          </div>
        </div>

        <div class="chart-type-selector">
          <span class="chart-type-label">图表类型：</span>
          <el-radio-group v-model="chartType" button-style="solid">
            <el-radio-button value="bar">
              <!-- <template #icon><bar-chart-outlined /></template> -->
              柱状图
            </el-radio-button>
            <el-radio-button value="line">
              <!-- <template #icon><line-chart-outlined /></template> -->
              折线图
            </el-radio-button>
            <el-radio-button value="pie">
              <!-- <template #icon><pie-chart-outlined /></template> -->
              饼图
            </el-radio-button>
          </el-radio-group>
        </div>

        <el-form-item required>
          <el-input
            v-model="prompt"
            type="textarea"
            :rows="8"
            :placeholder="getPlaceholder"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <!-- </div> -->
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel">取 消</el-button>
          <el-button type="primary" @click="handleOk" :loading="loading">生 成</el-button>
        </div>
      </template>
    </el-dialog>

    <PayModal
      v-if="modalStore.payModalVisible"
      v-model:model-value="modalStore.payModalVisible"
      @recharge="onRecharge"
      @confirm="onConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ACTION_CODE } from '@/utils/constants'
import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue'
import { UserService } from '@/services/user'

import { useUserStore } from '@/stores/user'
import { generateContent } from '@/api/user'
import { useModalStore } from '../../stores/modalStore'
import PayModal from '@/components/modal/PayModal.vue'
import { ElMessage } from 'element-plus'
import { safeJsonParse } from '@/utils/utils'

const userStore = useUserStore()
const modalStore = useModalStore()

const props = defineProps<{
  modelValue: boolean
  vipLevel?: number
  submissionId: string
  title?: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', content: {}): void
  (e: 'recharge', value: number): void
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (val) => {
    emit('update:modelValue', val)
  }
})

const prompt = ref('')
const loading = ref(false)
const chartType = ref<'bar' | 'line' | 'pie'>('bar')

const getPlaceholder = computed(() => {
  const placeholders = {
    bar: '请输入您需要生成的柱状图内容或补充要求',
    line: '请输入您需要生成的折线图内容或补充要求',
    pie: '请输入您需要生成的饼图内容或补充要求'
  }
  return placeholders[chartType.value]
})

watch(
  () => visible.value,
  (val) => {
    if (!val) {
      prompt.value = ''
      chartType.value = 'bar'
    }
  }
)
const sumbitData = async () => {
  if (!props.submissionId) {
    ElMessage.error('图表生成错误')
    return
  }
  loading.value = true
  try {
    let userContext = ''
    if (modalStore.referceText) {
      userContext = `${modalStore.referceText}, \n 请结合上下文生成图表`
    }
    const params: any = {
      submissionId: props.submissionId,
      code: ACTION_CODE.CHART,
      content: userContext || prompt.value,
      params: {
        chartType: chartType.value,
        title: props.title,
        ask: prompt.value
      }
    }
    if (userStore.getTeamId) {
      params.teamId = userStore.getTeamId
    }
    const response = await generateContent(params)
    // console.log('response ==>', response)
    if (response.ok && response.code == 200) {
      const jsonString = response.data.replace(/```json\n|\n```/g, '')
      const parsedResult: any = safeJsonParse(jsonString)

      emit('confirm', parsedResult)
      clearContent()
      await UserService.loadUserInfoAndAssistantMemberInfo()
    }
  } catch (error) {
    console.error('生成图表失败:', error)
    ElMessage.error('生成失败，请重试')
  } finally {
    loading.value = false
  }
}
const handleOk = async () => {
  if (!modalStore.referceText && !prompt.value) {
    ElMessage.warning('请选择参考文本或输入内容')
    return
  }
  // 打开支付弹窗
  modalStore.openPayModal()
}

const handleCancel = () => {
  if (loading.value) {
    ElMessage.warning('正在创作中，请勿关闭弹窗')
    return
  }
  clearContent()
}

const onRecharge = () => {
  emit('recharge', 1)
}

const onConfirm = () => {
  sumbitData()
}

const clearContent = () => {
  modalStore.closeAllModals()
  modalStore.closeReferceText()
  // emit('update:modelValue', false)
  visible.value = false
  prompt.value = ''
}

onMounted(() => {})
onBeforeUnmount(() => {
  clearContent()
})
</script>

<style lang="scss" scoped>
.form-container {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.reference-text {
  margin: 1.25rem 0;
  padding: 0.75rem 1.25rem;
  background-color: #ebf5ff;
  border-radius: 0.5rem;
  color: #4a5568;
}

.chart-type-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.chart-type-label {
  white-space: nowrap;
}

.text-ellipsis-wrapper {
  position: relative;
  overflow: hidden;
  height: 3em;
}
</style>
