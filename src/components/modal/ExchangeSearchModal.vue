<template>
  <el-dialog
    v-model="localVisible"
    title=""
    width="600px"
    center
    :destroy-on-close="true"
    @close="handleCancel"
    class="exchange-search-modal"
  >
    <div class="exchange-content">
      <!-- 选择区域 -->
      <div class="selection-area">
        <div class="modal-title">兑换学术搜索次数</div>
        <div class="selection-container">
          <el-radio-group v-model="selectedOptionIndex" class="exchange-radio-group">
            <el-radio-button :value="10">10次</el-radio-button>
            <el-radio-button :value="50">50次</el-radio-button>
            <el-radio-button :value="100">100次</el-radio-button>
            <el-radio-button :value="'custom'">自定义</el-radio-button>
          </el-radio-group>

          <!-- 自定义输入框 -->
          <el-input-number
            v-if="selectedOptionIndex === 'custom'"
            v-model="customTimes"
            :min="1"
            :max="99999999"
            :step="1"
            placeholder="请输入次数"
            class="custom-input"
            @change="handleCustomTimesChange"
          />
        </div>
      </div>

      <!-- 信息显示区域 -->
      <div class="info-card">
        <div class="info-row">
          <span class="info-label">消耗：</span>
          <span class="info-value primary-color"> {{ formatCoins(requiredCoins) }}硬币 </span>
        </div>
      </div>
      <div class="info-card balance-card">
        <div class="info-row">
          <span class="balance-label">
            <img
              class="coin-icon"
              src="https://static-1256600262.file.myqcloud.com/xiaoin-h5/icons/new-pc/yingbi.png"
            />
            剩余硬币：
          </span>
          <span class="info-value" :class="isInsufficientCoins ? 'error-color' : 'normal-color'">
            {{ formatCoins(userCoinBalance) }}
          </span>
        </div>
        <div v-if="isInsufficientCoins" class="insufficient-warning">
          硬币余额不足，请先<a class="recharge-link" @click="handleRecharge">充值</a>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel" :disabled="isExchanging">取消</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :loading="isExchanging"
          :disabled="isInsufficientCoins || selectedTimes <= 0"
        >
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { computed, onUnmounted, watch } from 'vue'
import { useExchangeSearchStore } from '@/stores/exchangeSearch'

const props = defineProps<{
  visible: boolean
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
  (e: 'recharge'): void
}>()

const exchangeStore = useExchangeSearchStore()
const {
  selectedOptionIndex,
  customTimes,
  isExchanging,
  selectedTimes,
  requiredCoins,
  userCoinBalance,
  isInsufficientCoins
} = storeToRefs(exchangeStore)

// 本地可见性状态，用于双向绑定
const localVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 防止背景滚动
const preventBodyScroll = () => {
  document.body.style.overflow = 'hidden'
  document.body.style.paddingRight = '0px'
}

const restoreBodyScroll = () => {
  document.body.style.overflow = ''
  document.body.style.paddingRight = ''
}

const handleRecharge = () => {
  emit('update:visible', false)
  emit('recharge')
}

// 监听外部visible变化，同步到store
watch(
  () => props.visible,
  (newValue) => {
    if (newValue) {
      exchangeStore.openExchangeModal()
      preventBodyScroll()
    } else {
      exchangeStore.closeExchangeModal()
      restoreBodyScroll()
    }
  }
)

// 组件卸载时恢复滚动
onUnmounted(() => {
  restoreBodyScroll()
})

// 处理自定义次数变化
const handleCustomTimesChange = (value: string | number | null) => {
  if (value !== null && typeof value === 'number') {
    exchangeStore.setCustomTimes(value)
    // 当用户输入自定义次数时，自动选中自定义选项
    if (selectedOptionIndex.value !== 'custom') {
      exchangeStore.selectOption('custom')
    }
  }
}

// 格式化硬币数量显示
const formatCoins = (coins: number): string => {
  if (coins >= 10000) {
    return `${(coins / 10000).toFixed(1)}万`
  }
  return coins.toString()
}

// 取消操作
const handleCancel = () => {
  emit('update:visible', false)
}

// 确认兑换
const handleConfirm = async () => {
  const isSuccess = await exchangeStore.performExchange()
  if (isSuccess) {
    emit('success')
  }
  if (!exchangeStore.isExchanging) {
    emit('update:visible', false)
  }
}
</script>

<style scoped>
/* 弹窗内容样式 */
.exchange-content {
  padding: 0;
}

/* 选择区域样式 */
.selection-area {
  margin-bottom: 24px;
}

.modal-title {
  font-size: 18px;
  text-align: center;
  color: #333333;
  margin-bottom: 20px;
  font-weight: bold;
}

.selection-container {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

/* 单选按钮组样式 */
.exchange-radio-group {
  flex: 1;
  display: flex;
  flex-wrap: nowrap;
  width: 100%;
}

.exchange-radio-group :deep(.el-radio-button) {
  flex: 1;
}

.exchange-radio-group :deep(.el-radio-button__inner) {
  border-color: #e5e5e5;
  color: #333333;
  transition: all 0.3s;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  border-radius: 0;
}

.exchange-radio-group :deep(.el-radio-button__inner:hover) {
  border-color: #2551b5;
  color: #2551b5;
}

.exchange-radio-group :deep(.el-radio-button.is-active .el-radio-button__inner) {
  background-color: #e7edfe;
  border-color: #2551b5;
  color: #2551b5;
}

.exchange-radio-group :deep(.el-radio-button:first-child .el-radio-button__inner) {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}

.exchange-radio-group :deep(.el-radio-button:last-child .el-radio-button__inner) {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}

/* 自定义输入框样式 */
.custom-input {
  width: 150px;
}

.custom-input :deep(.el-input__inner) {
  height: 40px;
  border-color: #e5e5e5;
  transition: all 0.3s;
  color: #333333;
  font-size: 14px;
  font-weight: 500;
}

.custom-input :deep(.el-input__inner:hover) {
  border-color: #2551b5;
}

.custom-input :deep(.el-input__inner:focus) {
  border-color: #2551b5;
}

/* 信息卡片样式 */
.info-card {
  background-color: #f7f7f7;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 10px;
}

.balance-card {
  margin-bottom: 0;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-label {
  font-size: 14px;
  color: #777777;
}

.balance-label {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #777777;
}

.coin-icon {
  width: 17px;
  height: 17px;
  margin-right: 5px;
}

.info-value {
  font-size: 14px;
  font-weight: 500;
}

.primary-color {
  color: #2551b5;
}

.normal-color {
  color: #333333;
}

.error-color {
  color: #f56c6c;
}

.insufficient-warning {
  font-size: 12px;
  color: #f56c6c;
  background-color: #fef0f0;
  padding: 8px;
  border-radius: 4px;
  margin-top: 12px;
}

.recharge-link {
  color: #2551b5;
  text-decoration: none;
  cursor: pointer;
}

.recharge-link:hover {
  text-decoration: underline;
}

/* 弹窗底部样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .exchange-search-modal :deep(.el-dialog) {
    margin: 16px;
    max-width: calc(100vw - 32px);
  }

  .exchange-search-modal :deep(.el-dialog__body) {
    padding: 16px;
  }

  .exchange-search-modal :deep(.el-dialog__footer) {
    padding: 12px 16px;
  }

  /* 移动端保持一行布局，但调整按钮大小 */
  .exchange-radio-group :deep(.el-radio-button__inner) {
    padding: 6px 8px;
    font-size: 13px;
    height: 36px;
  }

  /* 移动端容器可以换行显示输入框 */
  .selection-container {
    flex-wrap: wrap;
  }

  .custom-input {
    width: 100%;
    margin-top: 8px;
  }
}
</style>
