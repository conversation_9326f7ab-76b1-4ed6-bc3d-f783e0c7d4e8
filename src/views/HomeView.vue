<template>
  <div v-if="editor" class="page-container">
    <el-affix :offset="0">
      <div class="page-header">
        <div @click="backCreations" class="page-header-back">
          <img
            src="https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/editor-back.png"
            alt="back"
            style="width: 32px; height: 32px"
          />
        </div>
        <left-right-swiper-scroll>
          <div class="header-tools">
            <!-- <div v-if="width > 992" class="logo"></div> -->

            <div class="header-apply-title" v-if="submissionEditInfo?.creatorName">
              {{ submissionEditInfo.creatorName || '' }}
            </div>
            <div v-if="width > 992 && documentTitle !== 'AiEditor'" class="header-doc-title">
              {{ documentTitle || documentTitleBlank }}
            </div>
            <div class="save-status">
              {{ isLoadingSave ? '保存中' : '已保存' }}
            </div>

            <el-tooltip content="历史记录">
              <span v-if="recordingOpen" class="save-status-icon" @click="goHistory">
                <Iconfont name="lishibanben"></Iconfont
              ></span>
            </el-tooltip>
            <el-tooltip content="撤销(Ctrl + Z)">
              <el-icon
                @click="editor.chain().focus().undo().run()"
                :class="{
                  'toolbar-icon-button': true,
                  'toolbar-icon-button-disabled': !editor.can().chain().focus().undo().run()
                }"
              >
                <Iconfont name="undo"></Iconfont>
              </el-icon>
            </el-tooltip>
            <el-tooltip content="恢复(Ctrl + Y)">
              <el-icon
                class="toolbar-icon-button"
                @click="editor.chain().focus().redo().run()"
                :class="{
                  'toolbar-icon-button': true,
                  'toolbar-icon-button-disabled': !editor.can().chain().focus().redo().run()
                }"
              >
                <Iconfont name="redo"></Iconfont>
              </el-icon>
            </el-tooltip>
            <el-tooltip content="正文">
              <el-icon
                @click="editor.chain().focus().setParagraph().run()"
                :class="{
                  'toolbar-icon-button': true,
                  'toolbar-icon-button-is-active': editor.isActive('paragraph')
                }"
              >
                <Iconfont name="zhengwen"></Iconfont>
              </el-icon>
            </el-tooltip>
            <el-tooltip content="标题1">
              <el-icon
                @click="editor.chain().focus().toggleHeading({ level: 1 }).run()"
                :class="{
                  'toolbar-icon-button': true,
                  'toolbar-icon-button-is-active': editor.isActive('heading', {
                    level: 1
                  })
                }"
              >
                <Iconfont name="biaoti1"></Iconfont>
              </el-icon>
            </el-tooltip>
            <el-tooltip content="标题2">
              <el-icon
                @click="editor.chain().focus().toggleHeading({ level: 2 }).run()"
                :class="{
                  'toolbar-icon-button': true,
                  'toolbar-icon-button-is-active': editor.isActive('heading', {
                    level: 2
                  })
                }"
              >
                <Iconfont name="biaoti2"></Iconfont>
              </el-icon>
            </el-tooltip>
            <el-tooltip content="标题3">
              <el-icon
                @click="editor.chain().focus().toggleHeading({ level: 3 }).run()"
                :class="{
                  'toolbar-icon-button': true,
                  'toolbar-icon-button-is-active': editor.isActive('heading', {
                    level: 3
                  })
                }"
              >
                <Iconfont name="biaoti3"></Iconfont>
              </el-icon>
            </el-tooltip>
            <el-tooltip content="加粗">
              <el-icon
                @click="editor.chain().focus().toggleBold().run()"
                :class="{
                  'toolbar-icon-button': true,
                  'toolbar-icon-button-is-active': editor.isActive('bold')
                }"
              >
                <Iconfont name="jiacu1"></Iconfont>
              </el-icon>
            </el-tooltip>
            <el-tooltip content="斜体">
              <el-icon
                @click="editor.chain().focus().toggleItalic().run()"
                :class="{
                  'toolbar-icon-button': true,
                  'toolbar-icon-button-is-active': editor.isActive('italic')
                }"
              >
                <Iconfont name="xieti"></Iconfont>
              </el-icon>
            </el-tooltip>
            <el-tooltip content="下划线">
              <el-icon
                @click="editor.chain().focus().toggleUnderline().run()"
                :class="{
                  'toolbar-icon-button': true,
                  'toolbar-icon-button-is-active': editor.isActive('underline')
                }"
              >
                <Iconfont name="xiahuaxian"></Iconfont>
              </el-icon>
            </el-tooltip>
            <!-- <button @click="insertHighlightText">foo</button> -->
            <el-icon
              :class="{
                'toolbar-icon-button': true
              }"
            >
              <TablePopover v-if="editor" :editor="editor" />
            </el-icon>
            <el-tooltip content="上传图片">
              <el-icon
                @click="handleUploadImage"
                :class="{
                  'toolbar-icon-button': true
                }"
              >
                <Iconfont name="tupian" style="font-size: 13px"></Iconfont>
              </el-icon>
            </el-tooltip>

            <!-- <button
              @click="handlePressToolbarAIChange(ACTION_CODE.POLISH)"
              :disabled="!isSelectedSth"
            >
              改写
            </button>
            <button
              @click="handlePressToolbarAIChange(ACTION_CODE.EXPAND)"
              :disabled="!isSelectedSth"
            >
              扩写
            </button>
            <button
              @click="handlePressToolbarAIChange(ACTION_CODE.SHORTEN)"
              :disabled="!isSelectedSth"
            >
              缩写
            </button>
            <button
              :disabled="!isSelectedSth"
              @click="handlePressToolbarAIChange(ACTION_CODE.TRANSLATE)"
            >
              翻译
            </button> -->

            <!-- <button @click="handlePressExportHTML">HTML</button> -->
            <!-- <button @click="handlePressExportJSON">JSON</button> -->
            <!-- <button @click="handlePressMathematics">测试公式</button> -->

            <el-button v-if="isShowFreeCountButton" type="primary" class="free-times" plain>
              {{
                submissionEditInfo.aiUsableCount
                  ? `剩余免费${submissionEditInfo.aiUsableCount}次`
                  : '无免费次数'
              }}
            </el-button>

            <el-button
              type="primary"
              class="export-word"
              @click="handlePressExportWord"
              :loading="isExprotWordLoading"
              >导出Word</el-button
            >

            <el-button
              plain
              class="join-knowledge"
              @click="handlePressJoinKnowledge"
              :loading="isJoinKnowledgeLoading"
              >加入知识库</el-button
            >
          </div>
        </left-right-swiper-scroll>
      </div>
    </el-affix>
    <div class="page-content" v-if="editor">
      <!-- 左侧大纲区域 :class="`${headerList.length > 0 ? '' : 'hide'}`"-->
      <div class="doc-directory">
        <div class="doc-directory-title" :class="`${headerList.length > 0 ? '' : 'hide'}`">
          大纲
        </div>
        <ul class="doc-directory-list">
          <li
            v-for="item in headerList"
            :key="item.id"
            :class="`toc-entity toc-header${item.level} hasChildren`"
            @click="scrollToHeader(item.id)"
          >
            {{ item.text }}
          </li>
        </ul>
      </div>
      <!-- 中间内容区域 -->
      <div class="xiaoin-editor-frame">
        <div v-if="documentTitle !== 'AiEditor'">
          <el-input
            v-model="documentTitle"
            class="no-border-input xiaoin-title"
            :placeholder="documentTitleBlank"
            placeholder-style="color: blue;"
            autosize
            type="textarea"
            maxlength="50"
            @change="handleInputTitle"
            @focus="handleInputFocus"
            @input="handleInput"
          ></el-input>
        </div>
        <div class="xiaoin-editor-content">
          <div
            :class="
              editorData?.rich_abstract_cn &&
              Array.isArray(editorData?.rich_abstract_cn?.content) &&
              editorData?.rich_abstract_cn.content.length > 0
                ? 'abstracts'
                : 'hide'
            "
          >
            <h1 class="abstracts-title" id="abstracts-cn">摘要</h1>
            <editor-content :editor="editor0" />
          </div>
          <div
            :class="
              editorData?.rich_abstract_en &&
              Array.isArray(editorData?.rich_abstract_en?.content) &&
              editorData?.rich_abstract_en.content.length > 0
                ? 'abstracts'
                : 'hide'
            "
          >
            <h1 class="abstracts-title" id="abstracts-en">Abstract</h1>
            <editor-content :editor="editor1" />
          </div>

          <editor-content class="main-content" :editor="editor2" />
        </div>
      </div>
      <!-- 右侧操作栏区域 -->
      <div class="action-right">
        <!-- AI文本编辑区域 -->
        <div class="action-right-top">
          <div class="action-right-title">
            <div class="action-right-title-i action-right-title-icon action-right-title-icon-edit">
              <Iconfont name="zhinengpipei" style="font-size: 15px; color: #5076e6"></Iconfont>
            </div>
            <div>
              <div>AI文本编辑</div>
              <div class="action-right-desc">选中文字后，点击对应文本编辑工具</div>
            </div>
          </div>
          <div class="action-right-list">
            <!-- 改写区域 -->
            <div
              class="action-right-list-item action-right-list-item-ai-text-edit"
              @click="handlePressToolbarAIChange(ACTION_CODE.POLISH)"
            >
              <Iconfont name="gaixie" style="font-size: 24px"></Iconfont>
              <div>改写</div>
            </div>
            <!-- 扩写区域 -->
            <div
              class="action-right-list-item action-right-list-item-ai-text-edit"
              @click="handlePressToolbarAIChange(ACTION_CODE.EXPAND)"
            >
              <Iconfont name="kuoxie" style="font-size: 24px"></Iconfont>
              <div>扩写</div>
            </div>
            <!-- 缩写区域 -->
            <div
              class="action-right-list-item action-right-list-item-ai-text-edit"
              @click="handlePressToolbarAIChange(ACTION_CODE.SHORTEN)"
            >
              <Iconfont name="suoxie" style="font-size: 24px"></Iconfont>
              <div>缩写</div>
            </div>
            <!-- 翻译区域 -->
            <div
              class="action-right-list-item action-right-list-item-ai-text-edit"
              @click="handlePressToolbarAIChange(ACTION_CODE.TRANSLATE)"
            >
              <Iconfont name="fanyi" style="font-size: 24px"></Iconfont>
              <div>翻译</div>
            </div>
          </div>
        </div>
        <!-- 多模态AI生成区域 -->
        <div class="action-right-top">
          <div class="action-right-title">
            <div class="action-right-title-i action-right-title-icon action-right-title-icon-mode">
              <Iconfont name="duomotai" style="font-size: 15px; color: #7a4ae0"></Iconfont>
            </div>
            <div>
              <div>多模态AI生成</div>
              <div class="action-right-desc">选中文字后，点击对应多模态生成工具</div>
            </div>
          </div>
          <!-- <div> -->
          <el-row :gutter="10">
            <el-col :span="12">
              <!-- 示意图区域 -->
              <div class="action-right-mode svg" @click="handleOpenAIModal(ACTION_CODE.SCHEMATIC)">
                <Iconfont name="shiyitu" style="font-size: 28px; color: #5076e6"></Iconfont>
                <div>示意图</div>
              </div>
            </el-col>
            <el-col :span="12">
              <!-- 图表区域 -->
              <div class="action-right-mode chart" @click="handleOpenAIModal(ACTION_CODE.CHART)">
                <Iconfont name="tubiao" style="font-size: 28px; color: #7a4ae0"></Iconfont>
                <div>图表</div>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="10" style="margin-top: 10px">
            <el-col :span="8">
              <!-- 图片区域 -->
              <div class="action-right-mode image" @click="handleOpenAIModal(ACTION_CODE.IMAGE)">
                <Iconfont name="tupian1" style="font-size: 28px; color: #31a1c8"></Iconfont>
                <div>图片</div>
              </div>
            </el-col>
            <el-col :span="8">
              <!-- 表格区域 -->
              <div class="action-right-mode table" @click="handleOpenAIModal(ACTION_CODE.TABLE)">
                <Iconfont name="biaoge2" style="font-size: 28px; color: #5076e6"></Iconfont>
                <div>表格</div>
              </div>
            </el-col>
            <el-col :span="8">
              <!-- 公式区域 -->
              <div
                class="action-right-mode formula"
                @click="handleOpenAIModal(ACTION_CODE.FORMULA)"
              >
                <Iconfont name="gongshi" style="font-size: 28px; color: #e9824a"></Iconfont>
                <div>公式</div>
              </div>
            </el-col>
          </el-row>
          <!-- </div> -->
        </div>
        <!-- 学术优化区域 -->
        <div class="action-right-top" v-if="submissionEditInfo.isPaper">
          <div class="action-right-title">
            <div
              class="action-right-title-i action-right-title-icon action-right-title-icon-optimal"
            >
              <Iconfont name="xueshubiaoti" style="font-size: 15px; color: #1fa182"></Iconfont>
            </div>
            <div>学术优化</div>
          </div>
          <div class="academic-buttons-container">
            <!-- <div
              class="academic-button-base academic-search-button"
              @click="handleInsertAnnotation"
            >
              <Iconfont name="xueshusousuo" style="font-size: 28px; color: #337ed3"></Iconfont>
              <div>插入上标</div>
            </div> -->
            <div class="academic-button-base academic-search-button" @click="handleAcademicSearch">
              <Iconfont name="xueshusousuo" style="font-size: 28px; color: #337ed3"></Iconfont>
              <div>学术搜索</div>
            </div>
            <div class="academic-button-base academic-format-button" @click="handleAcademicFormat">
              <Iconfont name="wenxiangeshi" style="font-size: 28px; color: #d08529"></Iconfont>
              <div>文献格式</div>
            </div>
            <div class="academic-button-base academic-format-button" @click="handleChartSort">
              <Iconfont name="wenxiangeshi" style="font-size: 28px; color: #d08529"></Iconfont>
              <div>图表排序</div>
            </div>
          </div>
        </div>

        <div class="action-right-bottom" v-loading="isCreatingPPT">
          <div class="action-right-title">
            <div class="action-right-title-icon">
              <img src="https://static-1256600262.file.myqcloud.com/xiaoin-h5/logo/PPT.png" />
            </div>
            <div>文档转PPT</div>
          </div>

          <div class="action-right-bottom-explain">
            <div>
              <div class="action-right-bottom-explain-item">
                <Iconfont name="zhinengpipei" style="font-size: 13px"></Iconfont>
                <span>智能匹配</span>
              </div>
              <div style="margin-left: 5px">100%忠于原文内容生成</div>
            </div>
            <div style="margin-top: 15px">
              <div class="action-right-bottom-explain-item">
                <Iconfont name="zhuanyepaiban" style="font-size: 13px"></Iconfont>
                <span>专业排版</span>
              </div>
              <div style="margin-left: 5px">海量模版选择，专业图示效果</div>
            </div>
            <div style="margin-top: 15px">
              <div class="action-right-bottom-explain-item">
                <Iconfont name="shengshishengli" style="font-size: 13px"></Iconfont>
                <span>省时省力</span>
              </div>
              <div style="margin-left: 5px">只需几分钟，演讲、汇报轻松搞定</div>
            </div>
          </div>
          <div class="action-right-bottom-button" @click="handlePressOpenPPTCreate">
            <img
              src="https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/editor-action-right-button.png"
              style="width: 100%; height: auto"
            />
          </div>
        </div>
      </div>
    </div>
  </div>

  <div v-if="editor">
    <bubble-menu
      :class="
        submissionEditInfo.hasFreeCount
          ? 'bubble-menu-container bubble-menu-container-free'
          : 'bubble-menu-container'
      "
      :tippy-options="{ duration: 100, zIndex: 1 }"
      :editor="editor"
      v-if="shouldShow({ editor, state: editor.state })"
    >
      <div class="bubble-menu">
        <div class="bubble-tools">
          <el-icon
            @click="editor.chain().focus().setParagraph().run()"
            :class="{
              'toolbar-icon-button': true,
              'toolbar-icon-button-is-active': editor.isActive('paragraph')
            }"
          >
            <Iconfont name="zhengwen"></Iconfont>
          </el-icon>
          <el-icon
            @click="editor.chain().focus().toggleHeading({ level: 1 }).run()"
            :class="{
              'toolbar-icon-button': true,
              'toolbar-icon-button-is-active': editor.isActive('heading', {
                level: 1
              })
            }"
          >
            <Iconfont name="biaoti1"></Iconfont>
          </el-icon>

          <el-icon
            @click="editor.chain().focus().toggleHeading({ level: 2 }).run()"
            :class="{
              'toolbar-icon-button': true,
              'toolbar-icon-button-is-active': editor.isActive('heading', {
                level: 2
              })
            }"
          >
            <Iconfont name="biaoti2"></Iconfont>
          </el-icon>

          <el-icon
            @click="editor.chain().focus().toggleHeading({ level: 3 }).run()"
            :class="{
              'toolbar-icon-button': true,
              'toolbar-icon-button-is-active': editor.isActive('heading', {
                level: 3
              })
            }"
          >
            <Iconfont name="biaoti3"></Iconfont>
          </el-icon>
          <el-divider direction="vertical" />
          <el-icon
            @click="editor.chain().focus().toggleBold().run()"
            :class="{
              'toolbar-icon-button': true,
              'toolbar-icon-button-is-active': editor.isActive('bold')
            }"
          >
            <Iconfont name="jiacu1"></Iconfont>
          </el-icon>

          <el-icon
            @click="editor.chain().focus().toggleItalic().run()"
            :class="{
              'toolbar-icon-button': true,
              'toolbar-icon-button-is-active': editor.isActive('italic')
            }"
          >
            <Iconfont name="xieti"></Iconfont>
          </el-icon>

          <el-icon
            @click="editor.chain().focus().toggleUnderline().run()"
            :class="{
              'toolbar-icon-button': true,
              'toolbar-icon-button-is-active': editor.isActive('underline')
            }"
          >
            <Iconfont name="xiahuaxian"></Iconfont>
          </el-icon>
          <el-divider direction="vertical" />
          <!-- <button @click="handlePressToolbarInsert" :class="{ 'is-checked': isAIChangeChecked }">
              插
            </button> -->
          <button
            @click="handlePressToolbarAIChange(ACTION_CODE.POLISH)"
            :class="{ 'is-checked': isAIChangeChecked }"
          >
            改写
          </button>
          <button @click="handlePressToolbarAIChange(ACTION_CODE.EXPAND)">扩写</button>
          <button @click="handlePressToolbarAIChange(ACTION_CODE.SHORTEN)">缩写</button>
          <button @click="handlePressToolbarAIChange(ACTION_CODE.TRANSLATE)">翻译</button>
          <el-button v-if="isShowFreeCountButton" type="primary" class="free-times" plain>
            {{
              submissionEditInfo.aiUsableCount
                ? `剩余免费${submissionEditInfo.aiUsableCount}次`
                : '无免费次数'
            }}
          </el-button>
        </div>
      </div>
    </bubble-menu>
  </div>

  <el-drawer
    v-if="activeDoAction.visible"
    v-model="activeDoAction.visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="handleClose"
  >
    <template #title>
      <h4>{{ activeDoAction.codeText }}</h4>
    </template>

    <div class="bubble-total">
      <div class="bubble-panel" v-if="activeDoAction.isAIChanging">
        <!-- <div class="" v-if="activeDoAction.isLoading">
            <el-progress :percentage="100" :indeterminate="true" :show-text="false" />
          </div> -->
        <div v-if="activeDoAction.isLoading">
          <dot-loader />
        </div>
        <div v-else>
          <div v-if="activeDoAction.errorCode === ACTION_ERROR_CODE.NORMAL">
            <div style="white-space: pre-wrap">{{ activeDoAction.activeAIText }}</div>
            <div v-if="activeDoAction.activeAIText" style="text-align: right; margin-top: 50px">
              <el-button
                class="btn-normal"
                type="default"
                size="small"
                @click="handlePressPanelReplace"
                >替换</el-button
              >
              <el-button
                class="btn-normal btn-cancel"
                type="default"
                size="small"
                @click="handlePressPanelInsert"
                >插入</el-button
              >
            </div>
          </div>
          <div
            class="count-words"
            v-else-if="activeDoAction.errorCode === ACTION_ERROR_CODE.TWO_FEW_WORDS"
          >
            <div>已选{{ activeDoAction.countWords }}字</div>
            <div class="count-words-desc">
              选择<span class="red">篇幅长度不足</span>，请选中5字以上内后重试
            </div>
          </div>
          <div
            class="count-words"
            v-else-if="activeDoAction.errorCode === ACTION_ERROR_CODE.TWO_MANY_WORDS"
          >
            <div>已选{{ activeDoAction.countWords }}字</div>
            <div class="count-words-desc">
              选择<span class="red">篇幅长度超上限</span>，请拆分1000字以内
            </div>
          </div>
          <div
            class="count-words"
            v-else-if="activeDoAction.errorCode === ACTION_ERROR_CODE.SURE_COIN_BALANCE"
          >
            <div class="bubble-originalText">
              <div class="count-words">原文</div>
              <br />
              <div
                class="bubble-originalText-text"
                v-show="activeDoAction.originalText.length > 100 && !isFullTextVisible"
              >
                {{ activeDoAction.originalText.slice(0, 50) }}
                <a @click="showFullText" class="bubble-originalText-span"> [ ······· ] </a>
                {{ activeDoAction.originalText.slice(-50) }}
              </div>

              <div
                class="bubble-originalText-text"
                v-show="activeDoAction.originalText.length <= 100 || isFullTextVisible"
              >
                {{ activeDoAction.originalText }}
              </div>
            </div>

            <div>辅助信息</div>
            <br />
            <el-input
              v-model="textarea"
              style="width: 100%"
              :rows="5"
              type="textarea"
              placeholder="请输入清晰准确的AI修改要求，暂不支持字体、排版等格式修改要求。"
            />

            <div
              v-if="
                submissionEditInfo.aiUsableCount <= 0 && vipLevel !== 3 && store.getTeamId == ''
              "
            >
              <br />
              <div>剩余{{ getUnit() }}{{ coinBalance }}：</div>
              <div class="count-words-desc">
                本次<span class="blue">{{ activeDoAction.codeText }}</span
                >需消耗<span class="blue">
                  <template v-if="isXiaoin()">1万</template>
                  <template v-else>{{ 10000 / 1000 }}</template> </span
                >{{ getUnit() }}
                <!-- <div class="count-words-desc" v-if="activeDoAction.code === ACTION_CODE.SHORTEN">
                    目前只支持中文缩写，暂不支持英文缩写
                  </div> -->
                <div class="count-words-desc" v-if="activeDoAction.code === ACTION_CODE.TRANSLATE">
                  本次翻译仅支持中英互译
                </div>
              </div>
            </div>

            <div
              class="recharge-key"
              v-if="
                submissionEditInfo.aiUsableCount <= 0 && vipLevel !== 3 && store.getTeamId == ''
              "
            >
              <div class="recharge-key-text">升级小in会员，免费送编辑次数</div>
              <el-button
                type="primary"
                size="small"
                class="btn-normal to-recharge"
                @click="handleToRecharge(1)"
                >去升级</el-button
              >
            </div>

            <div style="text-align: right; margin-top: 50px">
              <el-button
                class="btn-normal"
                type="default"
                size="small"
                @click="handleOKAIAction(activeDoAction.code)"
                >确认</el-button
              >
              <el-button
                class="btn-normal btn-cancel"
                type="default"
                size="small"
                @click="handleCloseAIAction"
                >取消</el-button
              >
            </div>
          </div>
          <div
            class="count-words"
            v-else-if="activeDoAction.errorCode === ACTION_ERROR_CODE.COIN_SHORTAGE"
          >
            <div class="bubble-originalText">
              <div class="count-words">原文</div>
              <br />
              <div
                class="bubble-originalText-text"
                v-show="activeDoAction.originalText.length > 100 && !isFullTextVisible"
              >
                {{ activeDoAction.originalText.slice(0, 50) }}
                <a @click="showFullText" class="bubble-originalText-span"> [ ······· ] </a>
                {{ activeDoAction.originalText.slice(-50) }}
              </div>

              <div
                class="bubble-originalText-text"
                v-show="activeDoAction.originalText.length <= 100 || isFullTextVisible"
              >
                {{ activeDoAction.originalText }}
              </div>
            </div>

            <div>辅助信息</div>
            <br />
            <el-input
              v-model="textarea"
              style="width: 100%"
              :rows="5"
              type="textarea"
              placeholder="请输入清晰准确的AI修改要求，暂不支持字体、排版等格式修改要求。"
            />
            <div>
              <br />
              <div>剩余{{ getUnit() }}{{ coinBalance }}：</div>
              <div class="count-words-desc">
                本次<span class="blue">{{ activeDoAction.codeText }}</span
                >需消耗<span class="blue">{{ rewritingExpens }}</span
                >{{ getUnit() }}
              </div>

              <div class="recharge-key">
                <div class="recharge-key-text">升级小in会员，免费送编辑次数</div>
                <el-button
                  type="primary"
                  size="small"
                  class="btn-normal to-recharge"
                  @click="handleToRecharge(1)"
                  >去升级</el-button
                >
              </div>

              <div style="text-align: right; margin-top: 50px">
                <el-button
                  type="primary"
                  size="small"
                  class="btn-normal to-recharge"
                  @click="handleToRecharge(2)"
                  >去充值</el-button
                >
                <el-button
                  class="btn-normal btn-cancel"
                  type="default"
                  size="small"
                  @click="handleCloseAIAction"
                  >取消</el-button
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
  <PaymentDialog :rechargeKey="rechargeKey" v-model:model-value="rechargeVisible" />

  <MathGenerateModal
    v-if="modalStore.mathGenerateVisible"
    v-model:model-value="modalStore.mathGenerateVisible"
    :vip-level="vipLevel"
    :submissionId="submissionId"
    :title="submissionEditInfo?.title"
    @recharge="handleToRecharge(1)"
    @confirm="onConfirmMathGenerate"
  />
  <TableGenerateModal
    v-if="modalStore.tableGenerateVisible"
    v-model:model-value="modalStore.tableGenerateVisible"
    :vip-level="vipLevel"
    :submissionId="submissionId"
    :title="submissionEditInfo?.title"
    @recharge="handleToRecharge(1)"
    @confirm="onConfirmTableGenerate"
  />
  <ChartGenerateModal
    v-if="modalStore.chartGenerateVisible"
    v-model:model-value="modalStore.chartGenerateVisible"
    :vip-level="vipLevel"
    :submissionId="submissionId"
    :title="submissionEditInfo?.title"
    @recharge="handleToRecharge(1)"
    @confirm="onConfirmChartGenerate"
  />
  <ImgGenerateModal
    v-if="modalStore.imgGenerateVisible"
    v-model:model-value="modalStore.imgGenerateVisible"
    :vip-level="vipLevel"
    :submissionId="submissionId"
    :title="submissionEditInfo?.title"
    @recharge="handleToRecharge(1)"
    @confirm="onConfirmImgGenerate"
  />
  <SchematicGenerateModal
    v-if="modalStore.schematicGenerateVisible"
    v-model:model-value="modalStore.schematicGenerateVisible"
    :vip-level="vipLevel"
    :submissionId="submissionId"
    :title="submissionEditInfo?.title"
    @recharge="handleToRecharge(1)"
    @confirm="onConfirmSchematicGenerate"
  />

  <!-- 图表数据编辑弹窗 -->
  <ChartEditorModal
    v-model:visible="chartModalVisible"
    :chart-type="currentChartType"
    :editing-data="chartEditingData"
    @save="handleChartSave"
    @cancel="handleModalCancel"
  />

  <!-- 数学公式编辑弹窗 -->
  <MathEditorModal
    v-model:visible="mathModalVisible"
    :form-data="mathEditingData"
    @save="handleMathSave"
    @cancel="handleMathModalCancel"
  />

  <SchematicEditorModal
    v-model:visible="schematicSvgStore.visible"
    :submissionId="submissionId"
    @save="handleSvgSave"
  >
  </SchematicEditorModal>

  <!-- 学术搜索弹窗 -->
  <AcademicSearchModal
    v-if="academicSearchModalVisible"
    v-model="academicSearchModalVisible"
    :reference-text="currentReferenceText"
    :submissionId="submissionId"
    @confirm="handleAcademicSearchConfirm"
    @recharge="handleToRecharge(2)"
  />

  <!-- 文献格式弹窗 -->
  <AcademicFormatModal
    v-model="academicFormatModalVisible"
    :submissionId="submissionId"
    :loading="academicFormatLoading"
    @confirm="handleAcademicFormatConfirm"
  />

  <!-- 图表排序弹窗 -->
  <ChartSortModal
    ref="chartSortModalRef"
    v-model="chartSortModalVisible"
    :loading="chartSortLoading"
    @start="handleChartSortStart"
  />
</template>

<script setup lang="ts">
import MathGenerateModal from '@/components/modal/MathGenerateModal.vue'
import TableGenerateModal from '@/components/modal/TableGenerateModal.vue'
import ChartGenerateModal from '@/components/modal/ChartGenerateModal.vue'
import ImgGenerateModal from '@/components/modal/ImgGenerateModal.vue'
import SchematicGenerateModal from '@/components/modal/SchematicGenerateModal.vue'
import ChartEditorModal from '@/components/modal/ChartEditorModal.vue'
import MathEditorModal from '@/components/modal/MathEditorModal.vue'
import SchematicEditorModal from '@/components/modal/SchematicEditorModal.vue'
import AcademicFormatModal from '@/components/modal/AcademicFormatModal.vue'
import AcademicSearchModal from '@/components/modal/AcademicSearchModal.vue'
import ChartSortModal from '@/components/modal/ChartSortModal.vue'
import { BubbleMenu, Editor, EditorContent } from '@tiptap/vue-3'
import {
  ref,
  onMounted,
  onUnmounted,
  onBeforeUnmount,
  computed,
  unref,
  reactive,
  onErrorCaptured,
  nextTick
} from 'vue'
import Iconfont from '@/components/Iconfont.vue'
import { useRoute } from 'vue-router'
import { useRouter } from 'vue-router'
import { ElButton, ElMessage, ElMessageBox } from 'element-plus'
import TablePopover from '@/components/MenuCommands/TablePopover/index.vue'
import { getLast, doAiAction, getExportCode, save, Api } from '@/api/submissionEdit'
import { repositoryGetInfo } from '@/api/user'
import {
  extractReferencesRobust,
  replaceReferencesContent,
  scrollToReferenceSection
} from '@/utils/referenceUtils'

import {
  createTipTapEditor,
  countWords,
  sleep,
  getRewritingExpens,
  getUnit,
  isXiaoin
} from '@/utils/utils'
import { ACTION_CODE, ACTION_CODE_NAME, ACTION_ERROR_CODE } from '@/utils/constants'
import DotLoader from '@/components/DotLoader.vue'
import LeftRightSwiperScroll from '@/components/LeftRightSwiperScroll.vue'
import markdownit from 'markdown-it'
import _ from 'lodash'
import 'katex/dist/katex.min.css'
import { useWindowSize } from '@vueuse/core'
import { UserService } from '@/services/user'
import { useUserStore } from '@/stores/user'
import PaymentDialog from '@/components/PaymentDialog.vue'
import { isTableActive } from '@/utils/table'
import { saveDocumentVersion, getDocumentVersions } from '@/utils/db' // 引入封装的 db.js 模块
import { useSchematicSvgStore } from '@/stores/schematicSvgStore'
import { useModalStore } from '../stores/modalStore'
import type { AppUserInfo } from '@/services/types/loginMobileRes'
import type { EditorState } from '@tiptap/pm/state'
import { isLocalHost } from '@/utils/cross'
import { checkEditorFocus } from '@/utils/editor'
import { addFiles, exportWordToUrl } from '@/api/repositoryFile'
import { uploadByUrl } from '@/api/upload'
import { validateTextSelection, syncAnnotationCounterFromEditor } from '@/utils/annotationUtils'

interface BubbleMenuProps {
  editor: Editor
  state: EditorState
}

const { width } = useWindowSize()
const router = useRouter()

const editor = ref()
const editor0 = ref()
const editor1 = ref()
const editor2 = ref()
const documentTitle = ref('')
const documentTitleBlank = ref('未命名标题')
// const drawer = ref(false)

const schematicSvgStore = useSchematicSvgStore()
const modalStore = useModalStore()

const isAIChangeChecked = ref(false)
// const isAIChanging = ref(false)

const currentSelection = ref()
// const activeAIText = ref('')
// const isAIThinking = ref(false)
const editorData = ref()
const isLoadingSave = ref(false)
let editorDataLastValue: any

const rechargeVisible = ref(false)
const isCanEditor = ref(false)

const isModified = ref(false) // 标志，表示内容是否被修改

let autoSaveInterval: any

const recordingOpen = ref(false)
const isJoinKnowledgeLoading = ref(false)

const currentChartType = ref('bar')
const chartModalVisible = ref(false)
const mathModalVisible = ref(false)
const chartEditingData = ref({
  labels: '',
  datasets: [
    {
      label: '',
      data: ''
    }
  ]
})
const mathEditingData = ref({
  formula: '',
  type: 'inline'
})

// 学术功能弹窗状态
const academicSearchModalVisible = ref(false)
const academicFormatModalVisible = ref(false)
const currentReferenceText = ref('')

// 图表排序弹窗状态
const chartSortModalVisible = ref(false)
const chartSortLoading = ref(false)
const chartSortModalRef = ref()

const rechargeKey = ref(1) // 传递的 key

const activeDoAction = reactive({
  visible: false,
  code: '',
  codeText: '',
  originalText: '',
  activeAIText: '',
  isAIChanging: false,
  isLoading: false,
  countWords: 0,
  errorCode: ACTION_ERROR_CODE.NORMAL // 0正常， 1字数异常 2其他
})
let submissionId = ''
const updateDuration = 500
let currentUpdateField = ''

// -------------------------弹出层选中文字---------------------
const isFullTextVisible = ref(false)
const showFullText = () => {
  isFullTextVisible.value = true
}
// -------------------------弹出层选中文字---------------------

const onFocus = ({ editor: _editor, field }: { editor: any; field: string }) => {
  editor.value = _editor
  currentUpdateField = field
  isCanEditor.value = true
}
const updatebyData = ({ editor: _editor, field }: { editor: any; field: string }) => {
  console.log(_editor, 'updatebyData')
  // if (field === 'text' || field === 'main_content') {
  //   editor2.value = _editor
  // } else if (field === 'rich_abstract_cn') {
  //   editor0.value = _editor
  // } else if (field === 'rich_abstract_en') {
  //   editor1.value = _editor
  // }
  setTimeout(() => {
    let params: any = {}
    if (field === 'text') {
      params = {
        text: '',
        main_content: _editor.getJSON()
      }
    } else if (field === 'main_content') {
      params = {
        main_content: _editor.getJSON()
      }
    } else if (field === 'rich_abstract_cn') {
      params[field] = _editor.getJSON()
    } else if (field === 'rich_abstract_en') {
      params[field] = _editor.getJSON()
    }
    saveData(params)
  }, 300)
}

// 自动保存方法
const startAutoSave = () => {
  isModified.value = true
  // 清除上次定时器，重新计时
  if (autoSaveInterval) {
    clearTimeout(autoSaveInterval)
  }
  // 每隔3秒检查是否需要保存
  autoSaveInterval = setInterval(() => {
    if (isModified.value) {
      saveHistory() // 这里调用你的保存方法
    }
  }, 30 * 1000)
}

// 停止自动保存
const stopAutoSave = () => {
  if (autoSaveInterval) {
    clearTimeout(autoSaveInterval)
    autoSaveInterval = undefined
  }
}

const isCreatingPPT = ref(false)

const handlePressOpenPPTCreate = async () => {
  try {
    const id = await saveDataNoParams()
    if (!id) {
      ElMessage.error('缺少必要参数')
      return
    }
    isCreatingPPT.value = true
    const res = await getExportCode({ id })

    if (!res.success) {
      ElMessage.error(res.message || '获取导出码失败')
      return
    }

    const fileCode = res.data
    if (!fileCode) {
      ElMessage.error('获取文件导出码错误')
      return
    }

    const url = isLocalHost()
      ? `https://xiaoin-test.xingxiai.cn/create/ppt?id=${submissionId}&fileCode=${fileCode}&code=ppt`
      : `${location.protocol}//${location.hostname}/create/ppt?id=${submissionId}&fileCode=${fileCode}&code=ppt`

    // window.open(url)
    // 尝试打开新窗口
    // const newWindow = window.open(url, '_blank')
    // 创建一个临时链接并触发点击
    const link = document.createElement('a')
    link.href = url
    link.target = '_blank'
    link.rel = 'noopener noreferrer'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  } catch (error) {
    console.error('创建PPT时发生错误:', error)
    ElMessage.error('创建PPT失败，请稍后重试')
  } finally {
    isCreatingPPT.value = false
  }
}

const backCreations = () => {
  const url = isLocalHost()
    ? `https://xiaoin-test.xingxiai.cn/profile/creations`
    : `${location.protocol}//${location.hostname}/profile/creations`
  window.location.href = url
}

const onUpdate = ({ editor: _editor }: { editor: any }) => {
  startAutoSave() // 自动保存方法

  // 同步标注计数器
  try {
    syncAnnotationCounterFromEditor(_editor)
  } catch (error) {
    console.warn('同步标注计数器失败:', error)
  }

  if (isxml) {
    updatebyData({ editor: _editor, field: 'main_content' })
  } else if (editorData.value.text) {
    updatebyData({ editor: _editor, field: 'text' })
  } else if (editorData.value.main_content) {
    updatebyData({ editor: _editor, field: 'main_content' })
  }
  // console.log(_editor, 'onUpdate')
  // _editor = editor.value
  // // editor.value = _editor
  // setTimeout(() => {
  //   if (editorData.value.text) {
  //     saveData({
  //       text: _editor.getHTML()
  //     })
  //   } else if (editorData.value.main_content) {
  //     saveData({
  //       main_content: editor.value.getJSON()
  //     })
  //   }
  // }, 100)
}
const onUpdateRichAbstractCn = ({ editor: _editor }: { editor: any }) => {
  startAutoSave() // 自动保存方法
  updatebyData({ editor: _editor, field: 'rich_abstract_cn' })
}
const onUpdateRichAbstractEn = ({ editor: _editor }: { editor: any }) => {
  startAutoSave() // 自动保存方法
  updatebyData({ editor: _editor, field: 'rich_abstract_en' })
}
const isMarkdown = (text: string) => {
  // 检查是否包含标题（#）、列表（*）、加粗/斜体（*或_）、链接（[]()）等Markdown特征
  const markdownRegex = /(^#{1,6}\s)|(^-|\*\s)|(\*\*|__)|(\[.*?\]\(.*?\))|(\n(?=(\n+)))/gm
  return markdownRegex.test(text)
}
let submissionEditInfo = ref()
const route = useRoute()

function isXML(data: string) {
  try {
    const parser = new DOMParser()
    const doc = parser.parseFromString(data, 'application/xml')
    return doc.getElementsByTagName('parsererror').length === 0
  } catch (e) {
    return false
  }
}

let isxml = false

const updateActiveEditor = (newEditor: any) => {
  if (editor.value && editor.value !== newEditor) {
    editor.value = null // 清理旧的引用
  }
  editor.value = newEditor
}

const setupData = async () => {
  try {
    // 现在可以通过route对象访问路由参数，例如：
    const _submissionId = route.query?.id // 1773548357114007554
    if (!_submissionId) {
      // ElMessage.error('缺少参数')
      // 跳转到 404 页面
      console.log('跳转到 404 页面')
      router.push({ name: 'NotFound' }) // 根据路由配置的 name 跳转到 404 页面
      return
    }
    const _token = route.query?.t || ''
    if (_token) {
      UserService.onLoginByToken(_token.toString())
    }
    submissionId = _submissionId.toString()
    const res = await getLast({ submissionId })
    console.log(res, 'res')

    if (!res.success) {
      ElMessage.error(res.message || '绘图任务创建失败')
    }
    submissionEditInfo.value = res.data
    if (res?.data?.editorData) {
      const _editorData = res?.data?.editorData
      try {
        if (isXML(res.data.editorData)) {
          isxml = true

          const parser = new DOMParser()
          const xmlDoc = parser.parseFromString(res.data.editorData, 'text/xml')
          //           const bodyContent = xmlDoc.querySelector('body')?.innerHTML || '' // 防止没有 body 标签时报错
          const bodyContent = xmlDoc.querySelector('body')?.innerHTML || '' // 防止没有 body 标签时报错

          // 销毁并创建 TipTap 编辑器
          editor2.value?.destroy()
          try {
            editor2.value = createTipTapEditor({
              defaultContent: bodyContent,
              onFocus: ({ editor }) => onFocus({ editor, field: 'main_content' }),
              onUpdate: _.debounce(onUpdate, updateDuration),
              readonly: false,
              handleChartEdit,
              handleMathEdit,
              openSchematicEditor: handleOpenSchematicEditor
            })
          } catch (error) {
            console.log('createTipTapEditor error==>', error)
          }
          updateActiveEditor(editor2.value)
          documentTitle.value = res.data.title
          return
        }

        isxml = false

        const editorDataObj = JSON.parse(_editorData)
        documentTitle.value = editorDataObj?.replacements?.topic_cn || res.data?.title || ''
        editorData.value = editorDataObj
        editorDataLastValue = editorDataObj
        console.log(editorDataObj, 'editorDataObj')
        // text有可能是markdown
        let _text = ''
        if (editorDataObj.text) {
          _text = editorDataObj.text
          if (isMarkdown(_text)) {
            const md = markdownit({
              html: true, // 启用 HTML 标签
              linkify: true, // 自动将 URL 转换为链接
              breaks: true, // 将 \n 转换为 <br>
              typographer: true // 启用一些语言中立的替换和引号美化
            })
            _text = md.render(_text)
          }
        } else if (editorDataObj.main_content) {
          _text = editorDataObj.main_content
        }
        editor2.value = createTipTapEditor({
          defaultContent: _text,
          onFocus: ({ editor: _editor }) => {
            onFocus({ editor: _editor, field: 'main_content' })
          },
          onUpdate: _.debounce(onUpdate, updateDuration),
          readonly: false,
          handleChartEdit,
          handleMathEdit,
          openSchematicEditor: handleOpenSchematicEditor
        })
        updateActiveEditor(editor2.value)
        setHeaderList(editor2.value.getJSON())
        if (
          editorDataObj?.rich_abstract_cn &&
          Array.isArray(editorDataObj?.rich_abstract_cn?.content) &&
          editorDataObj?.rich_abstract_cn.content.length > 0
        ) {
          editor0.value = createTipTapEditor({
            defaultContent: editorDataObj.rich_abstract_cn,
            onFocus: ({ editor: _editor }) => {
              onFocus({ editor: _editor, field: 'rich_abstract_cn' })
            },
            onUpdate: _.debounce(onUpdateRichAbstractCn, updateDuration),
            readonly: false,
            handleChartEdit,
            handleMathEdit,
            openSchematicEditor: handleOpenSchematicEditor
          })
        }

        if (
          editorDataObj?.rich_abstract_en &&
          Array.isArray(editorDataObj?.rich_abstract_en?.content) &&
          editorDataObj?.rich_abstract_en.content.length > 0
        ) {
          editor1.value = createTipTapEditor({
            defaultContent: editorDataObj.rich_abstract_en,
            onFocus: ({ editor: _editor }) => {
              onFocus({ editor: _editor, field: 'rich_abstract_en' })
            },
            onUpdate: _.debounce(onUpdateRichAbstractEn, updateDuration),
            readonly: false,
            handleChartEdit,
            handleMathEdit,
            openSchematicEditor: handleOpenSchematicEditor
          })
        }
        await getUserInfo()
        await getInfo()
      } catch (error) {
        console.log(error)
      }
    }
  } catch (error) {
    console.log(error)
  }
}
const store = useUserStore()

// 添加全局错误捕获
onErrorCaptured((error, instance, info) => {
  console.error('HomeView组件错误捕获:', error, info)

  // 特殊处理DOM操作相关错误
  if (error.message?.includes('insertBefore') || error.message?.includes('emitsOptions')) {
    console.warn('DOM操作或组件销毁相关错误，已被捕获并处理')
    return false // 阻止错误继续传播
  }

  // 特殊处理JSON解析错误
  if (error.message?.includes('JSON') || error.message?.includes('SyntaxError')) {
    console.warn('JSON解析相关错误，已被捕获并处理')
    ElMessage.error('数据解析失败，请重试')
    return false // 阻止错误继续传播
  }

  return false
})

const coinBalance = computed(() => {
  if (!store.currentLoginInfo) {
    return '-'
  }
  const _coinBalance = store.currentLoginInfo?.coinBalance || 0
  if (_coinBalance < 0.1) {
    return _coinBalance
  }
  if (isXiaoin()) {
    return _coinBalance
  }
  return _coinBalance / 1000
})

// 充值成功后，重新加载次数和用户等级等信息
const onPaySuccess = async () => {
  ElMessage.success('充值成功')
  if (submissionId) {
    const res = await getLast({ submissionId })
    if (res.code == 500) {
      return
    }
    submissionEditInfo.value = res.data
  }
  // 重新加载用户信息
  // await getUserInfo()
  // await getInfo()

  await UserService.loadUserInfoAndAssistantMemberInfo()

  userInfo.value = store.currentLoginInfo
  vipLevel.value = store.knowledgeAssistantMemberInfo?.vipLevel
}
function onMessage(e: any) {
  let { command, value } = e.data

  switch (command) {
    case 'closeIframe': {
      if (!value) return
      rechargeVisible.value = false

      if (value?.paySuccess) {
        setTimeout(async () => {
          await onPaySuccess()
        }, 50)
      }
      break
    }
  }
}

// 处理打开示意图编辑器的函数
const handleOpenSchematicEditor = (svgData: any) => {
  // 如果有需要，可以在这里转换svgData为SchematicInfo格式
  // 这里我们假设svgData中包含了必要的信息
  if (svgData) {
    try {
      // 检查是否有schematicData（新的存储方式）
      if (svgData.schematicData) {
        // 设置示意图数据
        schematicSvgStore.setSchematicData(svgData.schematicData)
        // 设置编辑模式
        schematicSvgStore.setIsEdit(true)
        // 打开编辑器模态框
        schematicSvgStore.openModal()
      } else if (svgData.svgData) {
        // 兼容旧的数据格式
        schematicSvgStore.setSchematicData(svgData.svgData)
        schematicSvgStore.setIsEdit(true)
        schematicSvgStore.openModal()
      } else {
        console.warn('未找到有效的SVG数据')
      }
    } catch (error) {
      console.error('处理SVG数据时出错:', error)
    }
  }
}

// 监听图片加载完成事件
onMounted(() => {
  // console.log('HomeView mounted  version 2025-06-04')

  // editor0.value = createTipTapEditor(defaultContentOfAbstracts, onFocus)

  // editor1.value = createTipTapEditor(defaultContentOfAbstractsEn, onFocus)

  // editor2.value = createTipTapEditor(defaultContentOfContents, onFocus)

  // editor.value = editor0.value
  if (queryToken && queryToken.length > 20) {
    UserService.setToken(queryToken)
  }

  setupData()
  saveHistory()

  window.addEventListener('message', onMessage)
})

onBeforeUnmount(() => {
  if (editor.value) {
    editor.value.destroy()
  }
  stopAutoSave()
})
onUnmounted(() => {
  window.removeEventListener('message', onMessage)
})

// const isSelectedSth = computed(() => {
//   return (
//     editor.value.state.selection &&
//     editor.value.state.selection.empty === false &&
//     isCanEditor.value
//   )
// })

// 检查选区是否包含特殊节点
// const containsSpecialNodes = (state: EditorState) => {
//   try {
//     if (!state || !state.selection) return false

//     const { from, to } = state.selection
//     if (from === undefined || to === undefined || from >= state.doc.content.size || to <= 0)
//       return false

//     let hasSpecialNode = false

//     // 遍历选区内的所有节点
//     state.doc.nodesBetween(from, to, (node: any) => {
//       if (!node || !node.type || !node.type.name) return true
//       console.log('node.type.name ==>', node.type.name)
//       // 检查节点类型是否为特殊类型
//       if (
//         [
//           'image',
//           'table',
//           'tableRow',
//           'tableCell',
//           'tableHeader',
//           'mathInline',
//           'mathBlock',
//           'chart'
//         ].includes(node.type.name)
//       ) {
//         hasSpecialNode = true
//         return false // 停止遍历
//       }
//       return true // 继续遍历
//     })

//     return hasSpecialNode
//   } catch (err) {
//     console.error('Error in containsSpecialNodes--》:', err)
//     // return true
//     throw err
//   }
// }

// BubbleMenu显示条件
const shouldShow = (props: BubbleMenuProps): boolean => {
  try {
    // 如果AI编辑模式已经激活，始终显示菜单 ====== 此判断不能删除，否则确认弹窗显示时，BubbleMenu会隐藏
    // if (aiEditModeActive.value) return true;

    // 如果编辑器不可编辑或者正在生成内容，不显示菜单
    if (!props.editor.isEditable) return false

    // 选择为空时不显示
    if (props.state.selection.empty) return false

    // 检查选区是否在表格内 - 如果是，不显示菜单
    if (isTableActive(props.state)) return false

    // 检查选区是否包含特殊节点
    // if (containsSpecialNodes(props.state)) return false

    // 检查是否有选中的文本

    const { from, to } = props.state.selection
    const text = props.state.doc.textBetween(from, to, ' ')
    // 防止选择过程中BubbleMenu闪烁，只有当选区文本长度超过0时才显示

    return text.trim().length > 0
  } catch (error) {
    console.error('Error getting selected text:', error)
    return false
  }
}

// const isLinkSelection = computed(() => {
//   const { state } = editor.value
//   const { tr } = state
//   const { selection } = tr
//   return isLinkSelectionFun(selection)
// })
// const isTableActiveSelection = computed(() => {
//   return isTableActive(editor.value.state)
// })

// const isLinkSelectionFun = (selection: any) => {
//   if (!selection) return false
//   if (!selection?.node) {
//     return false
//   }

//   if (!selection?.node?.type) {
//     return false
//   }

//   if (['image', 'mathInline', 'mathBlock', 'chart'].includes(selection?.node?.type?.name)) {
//     return true
//   }

//   return false
// }

const rewritingExpens = ref(getRewritingExpens())
// const handlePressToolbarInsert = () => {}

// ----------------------获取用户信息----------------------

const textarea = ref('')

const userInfo = ref<AppUserInfo>() // 用户信息
const vipLevel = ref() // 用户会员信息

const getUserInfo = async () => {
  const res = await UserService.loadUserInfo()
  if (res) {
    userInfo.value = res
  }
}

const getInfo = async () => {
  const res = await repositoryGetInfo({ teamId: store.getTeamId })

  if (!res.ok) {
    return
  }
  vipLevel.value = res.data?.vipLevel
}
// 是否显示 免费次数的按钮
const isShowFreeCountButton = computed(() => {
  if (store.getTeamId !== '') {
    return false
  }
  if (submissionEditInfo.value == null) {
    return false
  }
  if (vipLevel.value == null) {
    return false
  }
  if (vipLevel.value == 3) {
    return false
  }
  if (!submissionEditInfo.value.hasFreeCount) {
    return false
  }
  return true
})

const handleSvgSave = (data: any) => {
  console.log('handleSvgSave 接收数据 ==>', data)

  try {
    // 准备SVG数据用于存储在图片节点属性中
    const svgDataForStorage = {
      schematicData: {
        title: data.title,
        items: data.items,
        fileName: data.fileName,
        count: data.count,
        type: data.type,
        nums: data.nums
      },
      imageUrl: data.svgUrl,
      svgWidth: data.svgWidth,
      svgHeight: data.svgHeight,
      docId: submissionId
    }

    // 创建图片节点，包含SVG数据
    const imageNode = {
      type: 'image',
      attrs: {
        src: data.svgUrl,
        alt: data.title || '示意图',
        title: data.title || '示意图',
        width: data.svgWidth,
        height: data.svgHeight,
        svgData: svgDataForStorage
      }
    }

    if (schematicSvgStore.isEdit) {
      // 编辑模式：更新现有图片的SVG数据
      const success = editor.value.commands.updateImageSvgData(data.svgUrl, svgDataForStorage)
      if (!success) {
        editor.value.chain().focus().insertContent(imageNode).run()
      }
    } else {
      if (editor.value) {
        try {
          // 获取当前选区
          const { from, to } = editor.value.state.selection
          // 确保编辑器有焦点
          if (!editor.value.isFocused) {
            editor.value.commands.focus()
          }

          // 检查是否有选中的内容
          const hasSelection = from !== to
          if (hasSelection) {
            // 有选中内容时，将光标移动到选区末尾，不替换内容

            editor.value.commands.setTextSelection(to)

            // 在选区末尾插入图片，添加换行确保格式正确
            const contentToInsert = [
              { type: 'paragraph', content: [] }, // 添加一个空段落作为分隔
              imageNode
            ]
            const success = editor.value.commands.insertContentAt(to, contentToInsert, {
              updateSelection: true,
              parseOptions: {
                preserveWhitespace: 'full'
              }
            })
            if (!success) {
              // 备用方案：先移动光标到末尾，再插入
              editor.value
                .chain()
                .focus()
                .setTextSelection(to)
                .insertContent([{ type: 'paragraph', content: [] }, imageNode])
                .run()
            }
          } else {
            // 没有选中内容时，在当前光标位置插入
            const success = editor.value.commands.insertContentAt(to, imageNode, {
              updateSelection: true,
              parseOptions: {
                preserveWhitespace: 'full'
              }
            })
            if (!success) {
              editor.value.chain().focus().insertContent(imageNode).run()
            }
          }
        } catch (error) {
          try {
            editor.value.chain().focus().insertContent(imageNode).run()
          } catch (fallbackError) {
            throw new Error('无法插入示意图内容')
          }
        }
      } else {
        throw new Error('编辑器未初始化')
      }
    }

    schematicSvgStore.setIsEdit(false)

    // 成功提示
    ElMessage.success(schematicSvgStore.isEdit ? '示意图更新成功' : '示意图插入成功')
  } catch (error: any) {
    // 根据错误类型给出不同的提示
    if (error?.message?.includes('编辑器未初始化')) {
      ElMessage.error('编辑器未准备就绪，请稍后重试')
    } else if (error?.message?.includes('无法插入示意图内容')) {
      ElMessage.error('插入示意图失败，请检查光标位置后重试')
    } else {
      ElMessage.error('处理示意图时出现错误，但内容可能已成功插入')
    }
    // 重置编辑状态
    schematicSvgStore.setIsEdit(false)
  }
}

const handleUploadImage = () => {
  if (!checkEditorFocus(editor.value, 'image')) {
    ElMessage.warning('请选择插入位置')
    return
  }
  editor.value.chain().focus().addImage().run()
}

const handlePressToolbarAIChange = async (code: string) => {
  currentSelection.value = editor.value.state.selection
  const { from, to } = editor.value.state.selection
  const selectedText = editor.value.view.state.doc.textBetween(from, to)

  activeDoAction.code = code
  activeDoAction.codeText = ACTION_CODE_NAME[code as keyof typeof ACTION_CODE_NAME]
  activeDoAction.visible = true
  activeDoAction.isLoading = true
  activeDoAction.isAIChanging = true
  activeDoAction.originalText = selectedText
  const _countWords = countWords(selectedText)
  activeDoAction.countWords = _countWords
  let errorCode = ACTION_ERROR_CODE.NORMAL
  if (_countWords < 6) {
    errorCode = ACTION_ERROR_CODE.TWO_FEW_WORDS
  } else if (_countWords > 1000) {
    errorCode = ACTION_ERROR_CODE.TWO_MANY_WORDS
  }
  if (errorCode == 0) {
    // if (submissionEditInfo.value.aiUsableCount < 1) {
    // const userInfo = await UserService.loadUserInfo()

    await getUserInfo()
    await getInfo()

    if (userInfo.value && userInfo.value.id) {
      if (
        (userInfo.value?.coinBalance || 0) < rewritingExpens.value &&
        submissionEditInfo.value.aiUsableCount <= 0
      ) {
        errorCode = ACTION_ERROR_CODE.COIN_SHORTAGE
        activeDoAction.isLoading = false
        activeDoAction.errorCode = errorCode
        return
      }
    }
    errorCode = ACTION_ERROR_CODE.SURE_COIN_BALANCE
    activeDoAction.isLoading = false
  } else {
    activeDoAction.isLoading = false
  }
  activeDoAction.errorCode = errorCode
}

const onConfirmImgGenerate = (result: any) => {
  if (result.content) {
    const content = {
      type: 'image',
      attrs: {
        src: result.content,
        alt: 'Inserted Image',
        title: 'Inserted Image'
      }
    }
    const selection = currentSelection.value
    if (selection) {
      const to = selection.to
      editor.value.commands.insertContentAt(to, content, {
        updateSelection: true,
        parseOptions: {
          preserveWhitespace: 'full'
        }
      })
    }
  }
}
const onConfirmChartGenerate = (result: any) => {
  console.log('onConfirmChartGenerate', result)
  editorInsertContent(result)
}
const onConfirmMathGenerate = (result: any) => {
  console.log('onConfirmMathGenerate', result)
  editorInsertContent(result)
}
const onConfirmTableGenerate = (result: any) => {
  console.log('onConfirmTableGenerate', result)
  try {
    editorInsertContent(result)
  } catch (error) {
    console.error('表格插入失败:', error)
    ElMessage.error('表格插入失败，请重试')
  }
}
const editorInsertContent = (result: any) => {
  if (editor.value) {
    const selection = editor.value.state.selection
    if (selection) {
      // 将光标移动到选择范围的末尾，而不是替换选中的文字
      editor.value.commands.setTextSelection(selection.to)
      // 然后在光标位置插入内容
      editor.value.commands.insertContent(result, {
        updateSelection: true,
        parseOptions: {
          preserveWhitespace: 'full'
        }
      })
    }
  }
}

const onConfirmSchematicGenerate = (result: any) => {
  console.log('onConfirmSchematicGenerate', result)
}

const handleModalCancel = () => {
  chartModalVisible.value = false
}

const handleChartEdit = ({ type, data }: { type: string; data: any }) => {
  currentChartType.value = type
  const labels = data.labels
  const dataset = data.datasets
  chartEditingData.value = {
    labels: labels.join(', '),
    // data: dataset.data.join(', '),
    // datasetLabel: type === 'pie' ? '' : dataset.label || ''
    datasets: dataset.map((dataset: any) => ({
      label: dataset.label || '',
      data: dataset.data.join(', ')
    }))
  }

  chartModalVisible.value = true
}

const handleMathEdit = ({ formula, type }: { formula: string; type: string }) => {
  mathEditingData.value = {
    formula,
    type
  }
  mathModalVisible.value = true
}

const handleChartSave = (data: any) => {
  if (editor.value) {
    editor.value
      .chain()
      .focus()
      .setChart({
        type: data.chartType,
        data: {
          labels: data.labels.split(',').map((item: string) => item.trim()),
          datasets: data.datasets
        }
      })
      .run()
  }
  chartModalVisible.value = false
}

const handleMathModalCancel = () => {
  mathModalVisible.value = false
}

const handleMathSave = (data: any) => {
  if (editor.value && data.formula) {
    if (data.type === 'inline') {
      editor.value.chain().focus().setMathInline(data.formula).run()
    } else {
      editor.value.chain().focus().setMathBlock(data.formula).run()
    }
  }
  mathModalVisible.value = false
}

function downloadFile(url: string, fileName: string) {
  const link = document.createElement('a')
  link.href = url
  link.download = fileName
  link.target = '_blank' // 可选，如果希望在新窗口中下载文件，请取消注释此行
  link.click()
}
const isExprotWordLoading = ref(false)
const handlePressExportWord = async () => {
  isExprotWordLoading.value = true
  await sleep(500)
  const id = await saveDataNoParams()
  if (!id) {
    return
  }
  const res = await getExportCode({ id })
  isExprotWordLoading.value = false
  if (!res.success) {
    return
  }
  const code = res.data
  if (code) {
    downloadFile(
      `${import.meta.env.VITE_APP_API_BASE_URL || ''}${Api.exportWord}?code=${code}`,
      id || `万能小in`
    )
  }
}

const handlePressJoinKnowledge = async () => {
  try {
    isJoinKnowledgeLoading.value = true

    // 1. 保存数据并获取ID
    const id = await saveDataNoParams()
    if (!id) {
      ElMessage.error('获取文档失败，无法获取文件')
      return
    }

    // 2. 获取导出码
    const exportCodeRes = await getExportCode({ id })
    if (!exportCodeRes.success || !exportCodeRes.data) {
      ElMessage.error(exportCodeRes.message || '获取导出码失败')
      return
    }

    const code = exportCodeRes.data

    const exportRes = await exportWordToUrl({ code: code })
    console.log('res1 ==>', exportRes)
    if (!exportRes.ok || !exportRes.data) {
      return
    }

    // 5. 上传文件
    const params = {
      fileName: `${documentTitle.value || '文档'}.docx`,
      fileUrl: exportRes.data
    }

    const fileData = await uploadByUrl(params)
    if (!fileData.ok || !fileData.data) {
      ElMessage.error(fileData.message || '文件上传失败')
      return
    }

    // 6. 添加到知识库
    const _response = {
      folderId: 0,
      fileIds: [fileData.data.id],
      spaceId: userInfo.value?.id
    }
    const res = await addFiles(_response)
    if (!res.ok) {
      ElMessage.error(res.message || '文件添加到知识库失败')
      return
    }

    ElMessage.success('文件添加到知识库成功')
  } catch (error) {
    console.error('加入知识库时发生错误:', error)
    ElMessage.error('加入知识库失败，请稍后重试')
  } finally {
    isJoinKnowledgeLoading.value = false
  }
}

const handleOKAIAction = async (code: string) => {
  activeDoAction.visible = true
  activeDoAction.isLoading = true
  activeDoAction.errorCode = ACTION_ERROR_CODE.NORMAL
  const res = await doAiAction({
    teamId: store.getTeamId,
    submissionId: submissionId,
    code,
    content: activeDoAction.originalText,
    params: {
      ask: textarea.value
    }
  })
  if (!res.success) {
    activeDoAction.isLoading = false
    return
  }
  activeDoAction.activeAIText = res.data || ''
  textarea.value = '' // 辅助信息框恢复默认
  activeDoAction.isLoading = false
  if (submissionEditInfo.value.aiUsableCount > 0) {
    submissionEditInfo.value.aiUsableCount--
  }
}

// const handleOpenSchematicEditorModal = () => {
//   schematicSvgStore.openModal()
// }

const handleCloseAIAction = () => {
  activeDoAction.visible = false
  textarea.value = '' // 辅助信息框恢复默认
  isFullTextVisible.value = false // 选中文字显示状态恢复默认
}
const handleClose = (done: any) => {
  if (activeDoAction.isLoading || activeDoAction.errorCode === ACTION_ERROR_CODE.NORMAL) {
    ElMessageBox.confirm('是否确认关闭?')
      .then(() => {
        done()
        textarea.value = '' // 辅助信息框恢复默认
        isFullTextVisible.value = false // 选中文字显示状态恢复默认
      })
      .catch(() => {
        // catch error
      })
  } else {
    done()
    textarea.value = '' // 辅助信息框恢复默认
    isFullTextVisible.value = false // 选中文字显示状态恢复默认
  }
}
const handlePressPanelReplace = () => {
  activeDoAction.visible = false

  let textToInsert = activeDoAction.activeAIText
  if (isMarkdown(textToInsert)) {
    const md = markdownit()
    textToInsert = md.render(textToInsert)
  }
  editor.value.chain().focus().insertContent(`${textToInsert}`).run()
}

const handlePressPanelInsert = () => {
  activeDoAction.visible = false
  let { to } = currentSelection.value

  let textToInsert = activeDoAction.activeAIText
  if (isMarkdown(textToInsert)) {
    const md = markdownit()
    textToInsert = md.render(textToInsert)
  }
  editor.value.commands.insertContentAt(to, textToInsert, {
    updateSelection: true,
    parseOptions: {
      preserveWhitespace: 'full'
    }
  })
}

const handleToRecharge = async (key: number) => {
  await UserService.loadUserInfo()
  rechargeKey.value = key
  rechargeVisible.value = true
}

const saveDataNoParams = async () => {
  let params: any = {}
  if (currentUpdateField === 'text') {
    params = {
      text: '',
      main_content: editor.value.getJSON()
    }
  } else if (currentUpdateField === 'main_content') {
    params = {
      main_content: editor.value.getJSON()
    }
  } else if (currentUpdateField === 'rich_abstract_cn') {
    params[currentUpdateField] = editor.value.getJSON()
  } else if (currentUpdateField === 'rich_abstract_en') {
    params[currentUpdateField] = editor.value.getJSON()
  }
  return await saveData(params, true)
}
const saveData = async (params: { replacements?: any }, isPure = false) => {
  if (!isPure) {
    isLoadingSave.value = true
  }
  console.log(params, 'save')

  const _editorData = {
    ...editorDataLastValue,
    ...params
  }
  const saveParams = {
    submissionId: submissionId,
    title: documentTitle.value,
    editorData: JSON.stringify(_editorData)
  }
  //把最新的值保留下来
  editorDataLastValue = {
    ..._editorData
  }
  if (!isPure) {
    setHeaderList(_editorData.main_content)
  }

  const res = await save(saveParams)
  if (!isPure) {
    isLoadingSave.value = false
  }

  if (!res.success) {
    return
  }
  if (!isPure) {
    submissionEditInfo.value = {
      ...unref(submissionEditInfo),
      id: res.data
    }
  }
  return res.data
}
// 创建一个debounced函数
const debounced = _.debounce(async (value: any) => {
  console.log('Input value changed:', value)
  const replacements = {
    ...editorData.value.replacements
  }
  replacements.topic_cn = value
  await saveData({ replacements })
}, 100) // 1000毫秒后执行

// 处理输入框的变化
const handleInput = (value: string) => {
  // 使用replace方法替换掉所有的换行符\n
  documentTitle.value = value.replace(/\n/g, '')
}
const handleInputTitle = (value: any) => {
  debounced(value)
}

const handleInputFocus = () => {
  editor.value.commands.blur()
  isCanEditor.value = false
}
const headerList = ref<any[]>([])
const setHeaderList = (json: any) => {
  const headers = []
  if (
    editorData.value?.rich_abstract_cn &&
    Array.isArray(editorData.value?.rich_abstract_cn?.content) &&
    editorData.value?.rich_abstract_cn.content.length > 0
  ) {
    headers.push({
      text: '摘要',
      level: 1,
      id: 'abstracts-cn'
    })
  }
  if (
    editorData.value?.rich_abstract_en &&
    Array.isArray(editorData.value?.rich_abstract_en?.content) &&
    editorData.value?.rich_abstract_en.content.length > 0
  ) {
    headers.push({
      text: 'Abstract',
      level: 1,
      id: 'abstracts-en'
    })
  }
  let textList: any[] = []
  if (json?.content && Array.isArray(json?.content) && json.content.length > 0) {
    json.content.forEach((item: any) => {
      const { type, attrs, content } = item
      if (type === 'heading' && content && content.length > 0) {
        textList = []
        content.forEach((d: any) => {
          if (d.text) {
            textList.push(d.text)
          }
        })
        headers.push({
          text: textList.join('') || '',
          ...attrs
        })
        // 假设你有方法获取所有该类型的节点并构造headers数组
        // headers.push(...this.getNodesByType(editor.value.state.doc, name))
      }
    })
  }
  headerList.value = headers
}
const scrollToHeader = (id: string) => {
  const element = document.querySelector(`[id="${id}"]`) // 假设每个标题都有一个唯一的id属性
  if (element) {
    // 使用浏览器原生的scrollIntoView方法，可以设置平滑滚动
    element.scrollIntoView({
      behavior: 'smooth', // 平滑滚动
      block: 'start', // 顶部对齐
      inline: 'nearest' // 最近边对齐
    })
  } else {
    console.error('无法找到对应的标题元素')
  }
}

const goHistory = async () => {
  // 如果没有版本数据，则直接返回
  if (!recordingOpen.value) {
    return
  }
  router.push({ path: '/history', query: { id: docId } })
}

// --------------------------历史记录保存到indexedDb----------------------------
const docId = route.query?.id
const queryToken = route.query.t ? route.query.t.toString() : ''

// 自动保存当前版本到历史
const saveHistory = async () => {
  if (isModified.value) {
    // 获取文档所有版本，计算下一个 versionId
    const loadedVersions = await getDocumentVersions(docId)
    // 设置 recordingList 的值
    recordingOpen.value = loadedVersions.length > 0

    const content = JSON.stringify({
      ...submissionEditInfo.value,
      editorData: JSON.stringify(editorDataLastValue)
    }) // 获取当前内容

    // 获取所有版本的 versionId 数字部分
    const versionNumbers = loadedVersions.map((version: any) => {
      const match = version.versionId.match(/^v(\d+)$/)
      return match ? parseInt(match[1], 10) : 0
    })

    // 获取最大版本号并累加
    const nextVersionNumber = Math.max(...versionNumbers, 0) + 1
    const nextVersionId = `v${nextVersionNumber}`

    await saveDocumentVersion(docId, nextVersionId, content)

    // // 提示保存成功
    // alert('版本已保存')

    isModified.value = false // 保存后标记为未修改
  }

  const recordingList = await getDocumentVersions(docId)
  // 设置 recordingList 的值
  // console.log(recordingList)
  recordingOpen.value = recordingList.length > 0
}

// handleOpenAIModal函数实现
const handleOpenAIModal = (code: string) => {
  try {
    currentSelection.value = editor.value.state.selection
    if (!editor.value?.state?.selection) {
      return
    }
    // eslint-disable-next-line no-unsafe-optional-chaining
    const { from, to } = editor.value?.state?.selection
    const selectedText = editor.value?.view.state.doc.textBetween(from, to)
    modalStore.setReferceText(selectedText)

    modalStore.openModal(code)
    if (code != ACTION_CODE.SCHEMATIC) {
      return
    }
    // 获取创作示意图的段落文本
    setSchematicReferceText(from, to, selectedText)
    modalStore.openModal(code)
  } catch (error) {
    console.log(error)
  }
}

// 学术搜索点击事件
const handleAcademicSearch = () => {
  try {
    if (!editor.value?.state?.selection) {
      ElMessage.warning('请在文档中选择需要进行学术搜索的文本内容')
      return
    }
    // 获取当前选中的文本作为参考文本
    const { from, to } = editor.value.state.selection
    const selectedText = editor.value.view.state.doc.textBetween(from, to)
    currentReferenceText.value = selectedText

    if (!selectedText) {
      ElMessage.warning('请在文档中选择需要进行学术搜索的文本内容')
      return
    }
    // 打开学术搜索弹窗
    academicSearchModalVisible.value = true
  } catch (error) {
    ElMessage.error('学术搜索功能暂时不可用，请稍后重试')
  }
}

// 插入标注点击事件
const handleInsertAnnotation = async (currentLiteratrueData: string) => {
  try {
    if (!editor.value) {
      ElMessage.warning('编辑器未初始化，请稍后重试')
      return
    }

    // 验证参考文献数据参数
    if (
      !currentLiteratrueData ||
      typeof currentLiteratrueData !== 'string' ||
      currentLiteratrueData.trim() === ''
    ) {
      ElMessage.warning('参考文献数据不完整，将只插入标注')
    }

    // 验证文本选择
    const validation = validateTextSelection(editor.value)

    if (!validation.isValid) {
      ElMessage.warning(validation.message)
      return
    }

    // 智能替换逻辑：检查选中文本中的上标标注
    let shouldUseSmartReplace = false
    let uniqueAnnotations: number[] = []

    if (validation.selection && currentLiteratrueData && currentLiteratrueData.trim()) {
      try {
        // 1. 解析选中内容中的上标标注（使用HTML解析以精确识别）
        const { from, to } = validation.selection
        let annotationsInSelection: number[] = []

        try {
          // 优先使用HTML解析方法
          const selectedHTML = getSelectedHTMLContent(editor.value, from, to)
          if (selectedHTML) {
            annotationsInSelection = parseAnnotationsFromHTML(selectedHTML)
          }
        } catch (htmlError) {
          console.warn('HTML解析方法失败，回退到文本解析:', htmlError)
        }

        // 如果HTML解析失败或没有结果，回退到文本解析方法
        if (annotationsInSelection.length === 0) {
          const selectedText = validation.selection.text
          annotationsInSelection = parseAnnotationsFromText(selectedText)
        }

        if (annotationsInSelection.length > 0) {
          // 2. 检查这些上标在全文中的引用情况
          const uniquenessCheck = checkAnnotationUniqueness(editor.value, annotationsInSelection)

          // 3. 找出唯一引用的上标
          uniqueAnnotations = annotationsInSelection.filter((num) => uniquenessCheck[num]?.isUnique)

          if (uniqueAnnotations.length > 0) {
            shouldUseSmartReplace = true
          }
        }
      } catch (error) {
        console.error('智能替换检查时发生错误:', error)
        // 发生错误时回退到原有逻辑
      }
    }

    // 调用编辑器的插入标注命令，传递参考文献数据
    const result = editor.value.commands.insertAnnotationSuperscript({
      referenceContent:
        currentLiteratrueData && currentLiteratrueData.trim()
          ? currentLiteratrueData.trim()
          : undefined
    })

    // 检查返回结果的格式
    if (result && typeof result === 'object' && result.success) {
      // 标注插入成功（参考文献插入已在扩展中处理）
      const annotationNumber = result.annotationNumber
      if (
        shouldUseSmartReplace &&
        uniqueAnnotations.length > 0 &&
        currentLiteratrueData &&
        currentLiteratrueData.trim()
      ) {
        try {
          // 对每个唯一引用的上标执行替换
          let replaceCount = 0
          for (const uniqueAnnotationNumber of uniqueAnnotations) {
            const replaceSuccess = await replaceUniqueReferenceContent(
              editor.value,
              uniqueAnnotationNumber,
              currentLiteratrueData.trim()
            )
            if (replaceSuccess) {
              replaceCount++
            }
          }

          if (replaceCount > 0) {
            ElMessage.success(`标注插入成功，智能替换了 ${replaceCount} 个参考文献条目`)
          } else {
            ElMessage.success(`标注插入成功，但智能替换失败，已使用常规插入方式`)
          }
        } catch (smartReplaceError) {
          ElMessage.success(`标注插入成功，智能替换失败，已使用常规插入方式`)
        }
      } else {
        // 常规处理逻辑
        if (currentLiteratrueData && currentLiteratrueData.trim()) {
          ElMessage.success(`标注和参考文献插入成功`)
        } else {
          ElMessage.success(`标注 [${annotationNumber}] 插入成功`)
        }
      }
    } else if (result && typeof result === 'boolean' && result) {
      // 兼容旧版本返回格式（布尔值）
      ElMessage.success(`标注插入成功，已为"${validation.selection?.text}"添加标注`)
    } else {
      // 插入失败
      const errorMessage =
        result && typeof result === 'object' && result.error ? result.error : '标注插入失败，请重试'

      ElMessage.error(errorMessage)
    }
  } catch (error) {
    ElMessage.error('插入标注功能暂时不可用，请稍后重试')
  }
}

// 文献格式点击事件
const handleAcademicFormat = async () => {
  try {
    // 使用鲁棒性提取参考文献
    const { references } = await extractReferencesRobust(editor.value)
    if (references.length === 0) {
      ElMessage.warning('未找到参考文献内容。')
      return
    }
    // 打开文献格式弹窗
    academicFormatModalVisible.value = true
  } catch (error) {
    ElMessage.error('参考文献处理失败，请稍后重试')
  }
}

/**
 * 获取选中内容的HTML格式
 * @param {any} editor - TipTap 编辑器实例
 * @param {number} from - 选择开始位置
 * @param {number} to - 选择结束位置
 * @returns {string} 选中内容的HTML字符串
 */
const getSelectedHTMLContent = (editor: any, from: number, to: number): string => {
  if (!editor || !editor.state || from === to) {
    return ''
  }

  try {
    // 方法1: 使用编辑器的schema和serializer
    const { state } = editor
    const slice = state.doc.slice(from, to)
    // 尝试使用schema的serializer
    if (state.schema && state.schema.serializer) {
      try {
        const fragment = state.schema.serializer.serializeFragment(slice.content)
        const tempDiv = document.createElement('div')
        tempDiv.appendChild(fragment)
        const htmlResult = tempDiv.innerHTML
        if (htmlResult && htmlResult.trim()) {
          return htmlResult
        }
      } catch (serializerError) {}
    }

    // 方法2: 使用编辑器内置的序列化器
    if (state.schema.serializer) {
      const fragment = state.schema.serializer.serializeFragment(slice.content)
      const tempDiv = document.createElement('div')
      tempDiv.appendChild(fragment)
      const htmlResult = tempDiv.innerHTML

      return htmlResult
    }

    const sliceJSON = slice.content.toJSON()

    if (sliceJSON && sliceJSON.content) {
      // 尝试从JSON结构中重建HTML
      let reconstructedHTML = ''

      for (const node of sliceJSON.content) {
        if (node.type === 'paragraph') {
          let paragraphHTML = '<p>'
          if (node.content) {
            for (const inline of node.content) {
              if (inline.type === 'text') {
                if (inline.marks) {
                  // 处理带标记的文本（如上标）
                  for (const mark of inline.marks) {
                    if (mark.type === 'superscript') {
                      paragraphHTML += `<sup>${inline.text}</sup>`
                    } else {
                      paragraphHTML += inline.text
                    }
                  }
                } else {
                  paragraphHTML += inline.text
                }
              }
            }
          }
          paragraphHTML += '</p>'
          reconstructedHTML += paragraphHTML
        }
      }

      if (reconstructedHTML) {
        return reconstructedHTML
      }
    }
    const fullHTML = editor.getHTML()
    const selectedText = state.doc.textBetween(from, to)

    if (selectedText && selectedText.trim()) {
      // 使用更精确的方法查找HTML中的对应部分
      const htmlLines = fullHTML.split('\n')
      let foundHTML = ''

      // 查找包含选中文本的行
      for (let i = 0; i < htmlLines.length; i++) {
        const line = htmlLines[i]
        if (line.includes(selectedText)) {
          // 找到包含文本的行，提取相关的HTML
          foundHTML = line
          break
        }
      }

      if (foundHTML) {
        return foundHTML
      }

      // 如果按行查找失败，尝试直接在HTML中查找
      const textIndex = fullHTML.indexOf(selectedText)
      if (textIndex !== -1) {
        // 向前和向后扩展，寻找完整的HTML标签
        let start = textIndex
        let end = textIndex + selectedText.length

        // 向前查找，直到找到标签开始或到达合理边界
        while (start > 0 && start > textIndex - 200) {
          if (fullHTML[start] === '<') {
            break
          }
          start--
        }

        // 向后查找，直到找到标签结束或到达合理边界
        while (end < fullHTML.length && end < textIndex + selectedText.length + 200) {
          if (fullHTML[end] === '>' && end > textIndex + selectedText.length) {
            end++
            break
          }
          end++
        }

        const extractedHTML = fullHTML.substring(start, end)
        return extractedHTML
      }
    }
    return ''
  } catch (error) {
    return ''
  }
}

/**
 * 从HTML内容中解析所有上标标注编号（优化版本）
 * @param {string} htmlContent - 要解析的HTML内容
 * @returns {number[]} 解析出的上标编号数组
 */
const parseAnnotationsFromHTML = (htmlContent: string): number[] => {
  if (!htmlContent || typeof htmlContent !== 'string') {
    return []
  }

  try {
    // 精确匹配上标HTML标签格式：<sup>[1]</sup>, <sup>[2]</sup>, 等
    const annotationRegex = /<sup>\[(\d+)\]<\/sup>/g
    const matches = [...htmlContent.matchAll(annotationRegex)]

    const numbers = matches
      .map((match) => parseInt(match[1], 10))
      .filter((num) => !isNaN(num) && num > 0)

    // 去重并排序
    return [...new Set(numbers)].sort((a, b) => a - b)
  } catch (error) {
    console.error('解析HTML中的上标标注时发生错误:', error)
    return []
  }
}

/**
 * 从文本中解析所有上标标注编号（向后兼容版本）
 * @param {string} text - 要解析的文本内容
 * @returns {number[]} 解析出的上标编号数组
 */
const parseAnnotationsFromText = (text: string): number[] => {
  if (!text || typeof text !== 'string') {
    return []
  }

  try {
    // 匹配多种上标格式：[1], [2], 等（保留原有逻辑作为备用）
    const annotationRegex = /\[(\d+)\]/g
    const matches = [...text.matchAll(annotationRegex)]

    const numbers = matches
      .map((match) => parseInt(match[1], 10))
      .filter((num) => !isNaN(num) && num > 0)

    // 去重并排序
    return [...new Set(numbers)].sort((a, b) => a - b)
  } catch (error) {
    console.error('解析上标标注时发生错误:', error)
    return []
  }
}

/**
 * 检查上标在全文中的引用情况
 * @param {any} editor - TipTap 编辑器实例
 * @param {number[]} annotationNumbers - 要检查的上标编号数组
 * @returns {object} 每个上标的引用情况
 */
const checkAnnotationUniqueness = (
  editor: any,
  annotationNumbers: number[]
): Record<number, { isUnique: boolean; count: number; positions: number[] }> => {
  if (!editor || !editor.state || !annotationNumbers.length) {
    return {}
  }

  try {
    const result: Record<number, { isUnique: boolean; count: number; positions: number[] }> = {}

    // 获取整个文档的HTML内容
    const htmlContent = editor.getHTML()

    annotationNumbers.forEach((num) => {
      // 匹配该编号的所有上标出现
      const regex = new RegExp(`<sup>\\[${num}\\]</sup>`, 'g')
      const matches = [...htmlContent.matchAll(regex)]

      result[num] = {
        isUnique: matches.length === 1,
        count: matches.length,
        positions: [] // 简化版本，不获取具体位置
      }
    })

    return result
  } catch (error) {
    return {}
  }
}

/**
 * 替换唯一引用的参考文献内容
 * @param {any} editor - TipTap 编辑器实例
 * @param {number} annotationNumber - 上标编号
 * @param {string} newContent - 新的参考文献内容
 * @returns {boolean} 是否成功替换
 */
const replaceUniqueReferenceContent = async (
  editor: any,
  annotationNumber: number,
  newContent: string
): Promise<boolean> => {
  if (!editor || !editor.state || !newContent || annotationNumber < 1) {
    return false
  }

  try {
    // 获取现有参考文献
    const extractResult = await extractReferencesRobust(editor)
    const existingReferences = extractResult.references || []

    if (existingReferences.length === 0) {
      console.warn('未找到现有参考文献，无法进行替换')
      return false
    }

    // 查找目标编号的参考文献
    let targetReferenceFound = false
    const updatedReferences = existingReferences.map((ref) => {
      const match = ref.text.match(/^\[(\d+)\]\s*(.+)/)
      if (match) {
        const refNumber = parseInt(match[1], 10)
        if (refNumber === annotationNumber) {
          targetReferenceFound = true
          return {
            ...ref,
            text: `[${annotationNumber}] ${newContent.trim()}`
          }
        }
      }
      return ref
    })

    if (!targetReferenceFound) {
      console.warn(`未找到编号为 [${annotationNumber}] 的参考文献`)
      return false
    }

    // 更新参考文献区域
    const formattedContent = updatedReferences.map((ref) => ref.text).join('\n')

    try {
      await replaceReferencesContent(editor, existingReferences, formattedContent)
      return true
    } catch (replaceError) {
      return false
    }
  } catch (error) {
    return false
  }
}

/**
 * 检测文本变化并更新选择状态到正确的插入位置
 * @param {string} action - 操作类型 ('insert' 或 'replace')
 * @param {string} insertedText - 插入/替换的文本内容
 * @returns {boolean} 是否成功更新选择状态
 */
const detectTextChangesAndUpdateSelection = (action: string, insertedText: string): boolean => {
  try {
    if (!editor.value || !currentSelection.value) {
      console.warn('编辑器或选择状态未初始化')
      return false
    }

    const originalSelection = currentSelection.value
    let newSelectionPosition: number

    if (action === 'replace') {
      // 替换操作：标注应插入到替换内容的结尾
      // 计算替换后的结束位置
      const insertedTextLength = insertedText.replace(/<[^>]*>/g, '').length // 移除HTML标签计算纯文本长度
      newSelectionPosition = originalSelection.from + insertedTextLength
    } else if (action === 'insert') {
      // 插入操作：标注应插入到新插入内容的结尾
      // 插入位置是原选择的结尾 + 插入内容的长度
      const insertedTextLength = insertedText.replace(/<[^>]*>/g, '').length
      newSelectionPosition = originalSelection.to + insertedTextLength

      console.log(
        `🔄 检测到插入操作，原始位置: ${originalSelection.from}-${originalSelection.to}, 新位置: ${newSelectionPosition}`
      )
    } else {
      console.warn('未知的操作类型:', action)
      return false
    }

    // 更新编辑器的选择状态到新位置
    // 创建一个小的选择区域在新内容的结尾，以满足 validateTextSelection 的要求
    // 选择新内容结尾的最后一个字符（如果存在）
    const selectionStart = Math.max(0, newSelectionPosition - 1)
    const selectionEnd = newSelectionPosition

    editor.value.commands.setTextSelection({ from: selectionStart, to: selectionEnd })

    // 更新 currentSelection 以反映新的状态
    currentSelection.value = {
      from: selectionStart,
      to: selectionEnd,
      empty: false
    }

    return true
  } catch (error) {
    return false
  }
}

// 学术搜索弹窗确认事件
const handleAcademicSearchConfirm = async (data: {
  action: string
  includeReferences: boolean
  currentLiteratrueData?: string
  polishContent?: string
}) => {
  try {
    activeDoAction.activeAIText = data.polishContent || ''
    currentSelection.value = editor.value.state.selection
    // 保存原始选择状态用于后续的位置计算
    // const originalSelection = currentSelection.value

    if (data.action === 'insert') {
      handlePressPanelInsert()
      await new Promise((resolve) => setTimeout(resolve, 100))
    } else if (data.action === 'replace') {
      handlePressPanelReplace()
      await new Promise((resolve) => setTimeout(resolve, 100))
    }

    if (data.includeReferences) {
      // 验证参考文献数据
      if (!data.currentLiteratrueData || data.currentLiteratrueData.trim() === '') {
        ElMessage.warning('参考文献数据为空，已跳过标注插入')
        return
      }

      try {
        // 检测文本变化并更新选择状态到正确的插入位置
        const insertedText = data.polishContent || ''
        const selectionUpdated = detectTextChangesAndUpdateSelection(data.action, insertedText)

        // 执行标注插入（包含参考文献插入）
        await handleInsertAnnotation(data.currentLiteratrueData)
      } catch (annotationError) {
        ElMessage.error('标注插入操作失败，请重试')
        return
      }
    }
  } catch (error) {
    ElMessage.error('学术搜索操作失败')
  }
}

// 添加 loading 状态
const academicFormatLoading = ref(false)

// 文献格式弹窗确认事件
const handleAcademicFormatConfirm = async (data: { format: string }) => {
  try {
    // 设置 loading 状态
    academicFormatLoading.value = true

    // 提取参考文献
    const { references } = await extractReferencesRobust(editor.value)
    if (references.length === 0) {
      ElMessage.warning('未找到参考文献内容。请确保文档中包含参考文献标题和文献条目。')
      return
    }

    const res = await doAiAction({
      teamId: store.getTeamId,
      submissionId: submissionId,
      code: ACTION_CODE.REFERENCES_FORMAT,
      content: references.map((ref: any) => ref.text).join('\n'),
      params: {
        ask: data.format
      }
    })

    if (!res.success) {
      ElMessage.error('文献格式整理失败')
      return
    }

    // 后台返回的res.data是格式化后的文献内容
    if (res.data) {
      await replaceReferencesContent(editor.value, references, res.data)
      ElMessage.success('文献格式整理完成')

      // 成功后关闭弹窗
      academicFormatModalVisible.value = false

      // 弹窗关闭后，滚动到参考文献区域
      // 使用 nextTick 确保弹窗完全关闭后再执行滚动
      await nextTick()

      // 添加短暂延迟，确保界面更新完成
      setTimeout(async () => {
        try {
          const scrollSuccess = await scrollToReferenceSection(editor.value)
        } catch (error) {}
      }, 300) // 300ms 延迟确保弹窗动画完成
    } else {
      ElMessage.warning('未获取到格式化后的文献内容')
    }
  } catch (error) {
    console.error('文献格式确认错误:', error)
    ElMessage.error('文献格式化失败')
  } finally {
    // 无论成功失败都要清除 loading 状态
    academicFormatLoading.value = false
  }
}

// 参考文献相关函数已移动到 @/utils/referenceUtils.ts

// 图表排序功能
const handleChartSort = () => {
  if (!editor.value) {
    ElMessage.error('编辑器未初始化')
    return
  }
  chartSortModalVisible.value = true
}

// 开始图表排序
const handleChartSortStart = async () => {
  if (!editor.value) {
    ElMessage.error('编辑器未初始化')
    return
  }

  chartSortLoading.value = true

  try {
    // 动态导入图表排序工具函数
    const { performChartSort } = await import('@/utils/chartSortUtils')

    // 执行图表排序
    const result = await performChartSort(editor.value)

    // 获取弹窗组件引用并设置结果
    const modalRef = chartSortModalRef.value
    if (modalRef) {
      modalRef.setResult(result)
    }

    if (result.success) {
      ElMessage.success(result.message)
    } else {
      ElMessage.warning(result.message)
    }
  } catch (error) {
    console.error('图表排序失败:', error)
    ElMessage.error('图表排序过程中发生错误')

    // 设置失败结果
    const modalRef = chartSortModalRef.value
    if (modalRef) {
      modalRef.setResult({
        success: false,
        message: '图表排序过程中发生错误，请重试',
        processedCount: 0,
        figureCount: 0,
        tableCount: 0,
        elements: []
      })
    }
  } finally {
    chartSortLoading.value = false
  }
}

const setSchematicReferceText = (from: any, to: any, selectedText: any) => {
  // 获取选中文本所在的完整段落
  let currentParagraphText = ''
  let beforeParagraphText = ''
  let afterParagraphText = ''

  // 存储查找到的段落节点信息
  const paragraphs: any[] = []
  let currentParagraphIndex = -1

  // 遍历文档获取所有段落
  editor.value.state.doc.descendants((node: any, pos: any) => {
    // 只处理段落和标题节点
    if (node.type.name === 'paragraph' || node.type.name === 'heading') {
      const paragraphFrom = pos
      const paragraphTo = pos + node.nodeSize

      // 保存段落信息
      paragraphs.push({
        node,
        from: paragraphFrom,
        to: paragraphTo,
        text: editor.value.view.state.doc.textBetween(paragraphFrom, paragraphTo)
      })

      // 如果选区在此段落内，记录当前段落索引
      if (from >= paragraphFrom && to <= paragraphTo) {
        currentParagraphIndex = paragraphs.length - 1
        // 获取完整的段落文本
        currentParagraphText = editor.value.view.state.doc.textBetween(paragraphFrom, paragraphTo)
      }
    }
    return true // 继续遍历
  })

  // 获取上一段和下一段
  if (currentParagraphIndex > 0) {
    beforeParagraphText = paragraphs[currentParagraphIndex - 1].text
  }

  if (currentParagraphIndex >= 0 && currentParagraphIndex < paragraphs.length - 1) {
    afterParagraphText = paragraphs[currentParagraphIndex + 1].text
  }

  // 当选择的是段落的部分内容时，使用完整段落文本
  if (
    currentParagraphText &&
    selectedText !== currentParagraphText &&
    selectedText.length < currentParagraphText.length
  ) {
    modalStore.setSchematicReferceText(currentParagraphText)
  } else {
    modalStore.setSchematicReferceText(selectedText)
  }

  // 设置上下文文本
  modalStore.setReferceBeforeText(beforeParagraphText)
  modalStore.setReferceAfterText(afterParagraphText)
}
</script>

<style lang="scss">
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden; // 移到最外层，允许横向滚动
  // overflow-y: hidden;
  background: #f5f7ff;
  min-width: 1360px; // 移到最外层，控制最小宽度

  .save-status-icon {
    margin: 0px 5px;
    padding-left: 5px;
    padding-right: 5px;
    width: 29px;
    height: 29px;
    line-height: 29px;
    // background-color: #f0f0f0;
    border-width: 0px;
    border-radius: 5px;
    cursor: pointer;
  }
  .save-status-icon:hover {
    /* hover 状态样式 */
    background-color: #f8f8f8;
  }

  .page-header {
    // background-color: green;
    min-height: 86px;
    // background-color: #f0f0f0;
    position: sticky;
    width: 100%;
    min-width: 1360px;
    // border-bottom: 1px solid #dddddd;
    padding: 18px;
    justify-content: center;
    align-items: center;
    display: flex;
    // box-shadow: 0 3px 15px -3px rgba(13, 20, 33, 0.13);
    background: linear-gradient(270deg, #e1e6ff 0%, #dbeafb 100%);
    // box-shadow: 0 3px 15px -3px #f0f0f0;
    // box-shadow: 2px 2px 2px 1px rgba(0, 0, 0, 0.2);
  }

  .page-header-back {
    width: 32px;
    height: 32px;
    margin-left: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: transparent;
    border: none;
    cursor: pointer;
  }

  .page-content {
    padding: 15px 0;
    flex: 1; // 占据所有剩余空间
    overflow: visible; // 改为visible，不再控制滚动
    background-color: #f5f7ff;
    display: flex;
    width: 100%; // 确保占满容器宽度
  }
  .free-times {
    height: 27px;
    background: #ecf6ff;
    border-radius: 5px;
    font-size: 13px;
    color: #3b82f6;
    line-height: 27px;
  }
  .export-word {
    height: 27px;
    background-image: linear-gradient(to right, #3b82f6, #6366f1);
  }
  .join-knowledge {
    height: 27px;
    color: #333333;
    background: #ffffff;
  }
  ol li p {
    padding: 0;
    text-indent: 0em;
  }

  .action-right {
    flex: 1; // 右侧操作栏占比1
    min-width: 324px; // 1360px * (1/4.2) ≈ 324px
    height: calc(100vh - 116px); // 设置明确高度：100vh减去header高度(86px)和padding(30px)
    overflow-y: auto;
    padding: 0 15px;

    .action-right-title {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      font-weight: bold;
      font-size: 16px;
      color: #333333;
      line-height: 25px;
      margin-bottom: 10px;
      span {
        margin-left: 5px;
      }
      .action-right-desc {
        font-weight: 400;
        font-size: 13px;
        color: #666e80;
        line-height: 19px;
        margin-top: 4px;
      }
      .action-right-title-icon {
        margin-right: 10px;
        height: 25px;
        line-height: 25px;
        img {
          width: 25px;
          height: 25px;
        }
      }
      .action-right-title-i {
        width: 25px;
        height: 25px;
        border-radius: 6px;
        text-align: center;
        line-height: 25px;
      }
      .action-right-title-icon-edit {
        background: #d9e9ff;
      }
      .action-right-title-icon-mode {
        background: #e3dfff;
      }
      .action-right-title-icon-optimal {
        background: #d7fbf3;
      }
    }

    .action-right-top {
      padding-bottom: 16px;
      .action-right-mode {
        text-align: center;
        font-size: 14px;
        font-weight: 400;
        line-height: 21px;
        color: #333;
        cursor: pointer;
      }
      .svg {
        background: linear-gradient(180deg, #e4f0ff 0%, #d1e4ff 100%);
        border-radius: 7px;
        padding: 17px 0 12px 0;
        div {
          margin-top: 7px;
          color: #5076e6;
        }
      }
      .chart {
        background: linear-gradient(180deg, #e3e5ff 0%, #d1d5ff 100%);
        border-radius: 7px;
        padding: 17px 0 12px 0;
        div {
          margin-top: 7px;
          color: #7a4ae0;
        }
      }
      .image {
        background: linear-gradient(180deg, #e3f6ff 0%, #c3e8f4 100%);
        border-radius: 7px;
        padding: 17px 0 12px 0;
        div {
          margin-top: 7px;
          color: #31a1c8;
        }
      }
      .table {
        background: linear-gradient(180deg, #ebefff 0%, #ced7fc 100%);
        border-radius: 7px;
        padding: 17px 0 12px 0;
        div {
          margin-top: 7px;
          color: #5076e6;
        }
      }
      .formula {
        background: linear-gradient(180deg, #fff3e8 0%, #fadfd2 100%);
        border-radius: 7px;
        padding: 17px 0 12px 0;
        div {
          margin-top: 7px;
          color: #e9824a;
        }
      }

      .action-right-list {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 7px;

        .action-right-list-item {
          text-align: center;
          font-size: 14px;
          font-weight: 400;
          line-height: 21px;
          color: #333;
          cursor: pointer;
          &:hover {
            color: #3b82f6;
          }
        }

        .action-right-list-item-ai-text-edit {
          background: #ffffff;
          border-radius: 7px;
          font-weight: 400;
          font-size: 14px;
          color: #354363;
          line-height: 21px;
          padding: 5px 17px;
        }
      }
    }

    .action-right-bottom {
      display: flex;
      flex-direction: column;

      .action-right-bottom-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .action-right-bottom-button {
        margin-top: 10px;
        cursor: pointer;
        position: relative;
      }

      .action-right-bottom-desc {
        font-size: 16px;
        line-height: 25px;
        font-weight: bold;
        margin: 14px 0;
        text-align: center;
      }
      .action-right-bottom-explain {
        background: #cce0ff;
        border-radius: 10px;
        padding: 20px 15px;

        .action-right-bottom-explain-item {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          width: 80px;
          span {
            font-size: 13px;
            font-weight: bold;
            line-height: 23px;
            color: #2551b5;
            margin-left: 5px;
            width: 60px;
          }
        }
        div {
          color: #4d6fbb;
          font-size: 11px;
          line-height: 19px;
          display: flex;
          justify-content: flex-start;
          align-items: center;
        }
      }
    }
  }

  /* 自定义滚动条样式 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(241, 245, 249, 0.8);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(to bottom, rgba(59, 130, 246, 0.3), rgba(147, 197, 253, 0.3));
    border-radius: 4px;
    transition: all 0.3s ease;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to bottom, rgba(59, 130, 246, 0.5), rgba(147, 197, 253, 0.5));
  }

  .doc-directory {
    flex: 1; // 左侧大纲占比1
    min-width: 324px; // 1360px * (1/4.2) ≈ 324px
    height: calc(100vh - 116px); // 设置明确高度：100vh减去header高度(86px)和padding(30px)
    overflow-y: auto;
    padding: 0 15px;

    ul {
      margin: 0;
      padding: 0;
      li {
        list-style: none;
      }
    }
    .doc-directory-title {
      color: #333;
      font-weight: bolder;
      padding: 6px 15px;
      font-size: 18px;
    }
    .doc-directory-list {
      padding-bottom: 55px;
      overflow-y: auto;
    }
  }

  // 移除原有的媒体查询隐藏逻辑，确保三栏始终显示
  .toc-entity {
    padding: 6px 6px 6px 15px;
    margin: 5px 0;
    line-height: 1.8;
    border: none;
    cursor: pointer;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    position: relative;
  }
  .toc-header1 {
    color: #666;
    font-weight: 700;
  }
  .toc-header2 {
    margin-left: 15px;
    padding-left: 15px;
  }
  .toc-header3 {
    margin-left: 15px;
    padding-left: 30px;
  }
}

/* Basic editor styles */
.tiptap {
  > * + * {
    margin-top: 0.75em;
  }

  ul,
  ol {
    padding: 0 1rem;
  }

  blockquote {
    padding-left: 1rem;
    border-left: 2px solid rgba(#0d0d0d, 0.1);
  }
}

.bubble-menu-container {
  box-shadow: 0 3px 15px -3px rgba(13, 20, 33, 0.13);
  -webkit-box-shadow: 0 3px 15px -3px rgba(13, 20, 33, 0.13);

  background-color: #ffffff;
  padding: 0.2rem;
  // border-radius: 0.5rem;
  border-radius: 0.1rem;

  border: 1px solid #eaeaea;
  // box-shadow: ;
  // min-width: 350px;
  // min-height: 50px;
  width: 540px;
}
.bubble-menu-container-free {
  width: 662px;
}

.bubble-menu {
  display: flex;
  padding: 10px;
}

.floating-menu {
  display: flex;
  background-color: #0d0d0d10;
  padding: 0.2rem;
  border-radius: 0.5rem;

  button {
    border: none;
    background: none;
    font-size: 0.85rem;
    font-weight: 500;
    padding: 0 0.2rem;
    opacity: 0.6;

    &:hover,
    &.is-active {
      opacity: 1;
    }
  }
}

.bubble-total {
  display: flex;
  flex-direction: column;
  height: 100%;

  .bubble-originalText {
    max-height: 50%;
    margin-bottom: 15%;
    overflow: auto;

    .bubble-originalText-text {
      line-height: 28px;
      color: #888888;
    }

    .bubble-originalText-span {
      cursor: pointer;
      color: #000000;
      font-size: 20px;
      font-weight: 500;
      &:hover {
        background-color: #f8f8f8;
      }
    }
  }
  .bubble-panel {
    max-height: 50%;
    // background-color: pink;
    // padding: 10px;
    // border-top: 1px solid #eaeaea;

    .recharge-key {
      margin-top: 20px;
      background: #ecf6ff;
      border-radius: 10px;
      border: 1px solid #3b82f6;
      display: flex;
      justify-content: space-between;
      padding: 17px 20px;
      align-items: center;
      &-text {
        font-weight: 400;
        font-size: 16px;
        color: #3b82f6;
        line-height: 25px;
      }
    }
  }
}

.title-background-container {
  position: relative;
  width: 100%;
  height: auto;
  background-image: url('https://static-1256600262.file.myqcloud.com/xiaoin-h5/image/editor-action-right-ppt.png');
  background-size: cover;
  background-position: center;
  border-radius: 8px;
  overflow: hidden;
  aspect-ratio: 16/9;
}

.title-overlay {
  position: absolute;
  top: 50%;
  left: 5%;
  transform: translateY(-50%);
  width: 50%;
  max-height: 75%;
  overflow: hidden;
  color: #fff;
  font-weight: bold;
  font-size: clamp(12px, 3vw, 16px);
  line-height: 1.5;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.7);
  display: -webkit-box;
  -webkit-line-clamp: 8;
  -webkit-box-orient: vertical;
  text-align: left;
  padding: 2%;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 5px;
}

/* 屏幕设备的样式 */
@media (min-width: 1280px) {
  .title-overlay {
    width: 55%;
    -webkit-line-clamp: 6;
    font-size: clamp(16px, 2.5vw, 14px);
  }
}

/* 非常小的屏幕设备的样式 */
@media (min-width: 1920px) {
  .title-overlay {
    width: 60%;
    -webkit-line-clamp: 4;
    font-size: clamp(20px, 2vw, 12px);
  }
}

.xiaoin-title {
  margin: 15px 0;
  font-family: STSongti-SC, STSongti-SC;
  font-weight: 900;
  font-size: 30px;
  color: #000000;
  line-height: 53px;
}
.xiaoin-editor-frame {
  flex: 2.2; // 中间内容区域占比2.2
  min-width: 712px; // 1360px * (2.2/4.2) ≈ 712px
  padding: 50px 50px;
  background-color: #fff;
  overflow-y: auto;
  height: calc(100vh - 116px); // 设置明确高度：100vh减去header高度(86px)和padding(30px)
}

@media (max-width: 768px) {
  .xiaoin-editor-frame {
    padding: 50px 20px;
  }
}

.xiaoin-editor-content {
  font-family: STSongti-SC, 'Heiti SC Medium', 'Heiti SC Light', SimSun;
  font-weight: 400;
  font-size: 18px;
  color: #000000;
  h1 {
    // margin-top: 30px;
    // margin-bottom: 50px;
    font-size: 22px;
    line-height: 37px;
  }

  h2 {
    // margin-top: 30px;
    // margin-bottom: 30px;
    font-size: 20px;
    line-height: 33px;
  }
  h3 {
    font-size: 19px;
    line-height: 31px;
  }

  p {
    margin: 0;
    margin: 15px 0;
    text-indent: 2em;
    font-size: 16px;
    line-height: 31px;
  }

  p.reference-item {
    text-indent: 0em;
  }
  p.no-indent {
    text-indent: 0em;
    padding: 0;
    margin: 0;
    font-size: 16px;
    line-height: 31px;
    &:first-child {
      padding-top: 15px;
    }
  }
  // .no-indent {
  //   &:first-child {
  //     margin-top: 15px;
  //     padding-top: 15px;
  //   }
  // }
  .abstracts-title {
    margin-top: 30px;
    margin-bottom: 0;
  }
  .main-content {
    h1 {
      margin-top: 30px;
      margin-bottom: 0;
    }
    h2,
    h3 {
      margin-top: 15px;
      margin-bottom: 0;
    }
  }
  h1 + .no-indent {
    margin-top: 15px;
  }
  img {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 1em auto;
    cursor: pointer;

    &.ProseMirror-selectednode {
      outline: 2px solid #4299e1;
      border-radius: 2px;
    }
  }

  .image-resizer {
    display: inline-flex;
    position: relative;
    flex-grow: 0;

    &.ProseMirror-selectednode img {
      outline: 2px solid #4299e1;
      border-radius: 2px;
    }

    .resize-handle {
      position: absolute;
      width: 12px;
      height: 12px;
      border: 1px solid #4299e1;
      background: white;
      border-radius: 2px;

      &.top-left {
        top: -6px;
        left: -6px;
        cursor: nw-resize;
      }

      &.top-right {
        top: -6px;
        right: -6px;
        cursor: ne-resize;
      }

      &.bottom-left {
        bottom: -6px;
        left: -6px;
        cursor: sw-resize;
      }

      &.bottom-right {
        bottom: -6px;
        right: -6px;
        cursor: se-resize;
      }
    }
  }

  .image-uploading {
    max-width: 100%;
    padding: 0;
    margin: 0;
    margin-left: -38px;
  }
}

.PromiseMirror-focused {
  border-width: 0px;
  outline: -webkit-focus-ring-color auto 0px;
}

:focus-visible {
  outline: -webkit-focus-ring-color auto 0px;
}
.header-tools,
.bubble-tools {
  white-space: nowrap;
  .toolbar-icon-button {
    margin: 0px 5px;
    padding-left: 5px;
    padding-right: 5px;
    width: 29px;
    height: 29px;
    // background-color: #f0f0f0;
    border-width: 0px;
    border-radius: 5px;

    cursor: pointer;
    &:hover {
      background-color: #f8f8f8;
    }

    .back-reations {
      width: 50px;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .toolbar-icon-button-is-active {
    color: rgb(21, 112, 239);
  }

  .toolbar-icon-button-disabled {
    color: #999999;
  }

  button {
    // width: 30px;
    // height: 30px;
    margin-left: 5px;
    margin-right: 5px;
    border-width: 0px;
    border-radius: 5px;
    background-color: transparent;
    white-space: nowrap;
    font-size: 14px;
    cursor: pointer;
    &:disabled {
      cursor: not-allowed;
    }
    &:hover {
      background-color: #f8f8f8;
    }
  }
  .logo {
    margin-right: 40px;
    width: 170px;
    height: 42px;
  }

  .header-apply-title {
    background: #f5f7ff;
    padding: 2px 15px;
    color: #2551b5;
    font-weight: 400;
    font-size: 15px;
    line-height: 23px;
    border-radius: 7px;
    margin-right: 20px;
  }

  .header-doc-title {
    padding: 0 5px;
    max-width: 210px;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 500;
    font-size: 15px;
    color: #333333;
    white-space: nowrap;
  }
  .save-status {
    padding: 0 10px;
    font-size: 14px;
    color: #999999;
    white-space: nowrap;
  }
}

.header-tools {
  display: flex;
  align-items: center;
}

// .toolbar-icon-button{
//   background-color: pink;
// }

.highlight {
  background-color: yellow;
  // transition: background-color 2s;

  animation: fadeOut 3s forwards;
}

@keyframes fadeOut {
  from {
    background-color: yellow;
  }

  to {
    background-color: transparent;
  }
}

.toolbar-ddl {
  padding: 0 5px;
  // background-color: pink;
  display: inline-block;
  text-align: center;
  // padding-left: 5px;
  // padding-top: 20px;
  height: 15px;
  // display:  inline-flex;
  // align-items: center;
  // justify-content: center;
}

/* Table-specific styling */
.tiptap {
  table {
    border-collapse: collapse;
    table-layout: fixed;
    width: 100%;
    margin: 0;
    overflow: hidden;
    text-indent: 0em;

    p {
      text-indent: 0em;
      padding: 0;
    }

    td,
    th {
      min-width: 1em;
      border: 2px solid #ced4da;
      padding: 3px 5px;
      vertical-align: top;
      box-sizing: border-box;
      position: relative;
      text-indent: 0em;
      > * {
        margin-bottom: 0;
      }
    }

    th {
      font-weight: bold;
      text-align: left;
      background-color: #f1f3f5;
      text-indent: 0em;
    }

    .selectedCell:after {
      z-index: 2;
      position: absolute;
      content: '';
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      background: rgba(200, 200, 255, 0.4);
      pointer-events: none;
    }

    .column-resize-handle {
      position: absolute;
      right: -2px;
      top: 0;
      bottom: -2px;
      width: 4px;
      background-color: #adf;
      pointer-events: none;
    }

    p {
      margin: 0;
    }
  }
}

/* Basic editor styles */
.ProseMirror {
  > * + * {
    margin-top: 0.75em;
  }

  .Tiptap-mathematics-editor {
    background: #202020;
    color: #fff;
    font-family: monospace;
    padding: 0.2rem 0.5rem;
  }

  .Tiptap-mathematics-render {
    cursor: pointer;
    padding: 0 0.25rem;
    transition: background 0.2s;

    &:hover {
      background: #eee;
    }
  }

  .Tiptap-mathematics-editor,
  .Tiptap-mathematics-render {
    border-radius: 0.25rem;
    display: inline-block;
    text-indent: 0em;
  }
}

.file-break::after {
  content: '分页符';
  // display: block;
  // color: white;
  // background-color: gray;
  // margin-bottom: 10px;
  // text-align: center;
  display: block;
  // display: flex;
  color: #5d6a91;
  background-color: #dde3ec;
  margin-bottom: 10px;
  text-align: center;
  justify-content: center;
  border: 1px solid #9cb0c9;
  border-radius: 3px;
  padding-top: 5px;
  padding-bottom: 5px;
}

.toc {
  text-align: center;
}

.toc::after {
  content: '目录';
  display: block;
  // display: flex;
  color: #5d6a91;
  background-color: #dde3ec;
  margin-bottom: 10px;
  text-align: center;
  justify-content: center;
  border: 1px solid #9cb0c9;
  border-radius: 3px;
  padding-top: 5px;
  padding-bottom: 5px;
}

.document-title-input {
  background-color: pink;
  border: none;
  outline: none;
  box-shadow: 0 0 0 0;

  .is-focus {
    border: none;
    outline: none;
    box-shadow: 0 0 0 0;
  }

  .el-input__wrapper {
    box-shadow: 0 0 0 0;
    outline: none;
  }

  .el-input {
    border: none;
    box-shadow: 0 0 0 0;
    outline: none;
  }

  input {
    border: none;
    outline: none;
    box-shadow: 0 0 0 0;
  }
}

.no-border-input {
  input,
  textarea {
    // font-size: 28px;
    outline: none;
    border: none;
    -webkit-box-shadow: none;
    box-shadow: none;
    // font-size: 22.5pt;
    // font-weight: 700;
    // color: #1f1f1f;
    // height: 50px;
    padding: 0;
    text-align: center;

    font-family: STSongti-SC, STSongti-SC;
    font-weight: 900;
    font-size: 30px;
    color: #000000;
    font-size: 26px;
    line-height: 45px;
    height: 45px;
    word-break: break-all;
  }
}

.no-border-input .el-input__wrapper {
  border: none !important;
  box-shadow: none !important;
  padding-left: 3px;
  padding-right: 3px;
}

.no-border-input .el-input__wrapper:focus {
  border-color: transparent !important;
  box-shadow: none !important;
}
.el-drawer__header {
  padding: 0 20px;
  margin: 0;
}
.count-words {
  font-weight: 500;
  font-size: 16px;
  color: #333333;
}
.count-words-desc {
  padding-top: 10px;
  font-weight: 400;
  font-size: 16px;
  color: #333333;
}
.red {
  color: #ff4242;
}
.blue {
  color: #3b82f6;
}
.btn-normal {
  width: 68px;
  height: 29px;
  line-height: 29px;
  text-align: center;
  border-radius: 5px;
  border: 1px solid #3b82f6;
  font-size: 14px;
  color: #3b82f6;
}
.to-recharge {
  background: linear-gradient(to right, #3b82f6, #6366f1);
  border: none;
  color: #fff;
  :hover {
    opacity: 0.85;
  }
}
.btn-cancel {
  margin-left: 30px !important;
}

// 学术优化按钮样式
.academic-buttons-container {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  gap: 8px;
  flex-shrink: 0;
}

.academic-button-base {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 7px;
  flex: 1;
  flex-shrink: 0;
  padding: 12px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  border: none;
  outline: none;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: translateY(0);
  }

  .iconfont {
    margin-bottom: 4px;
  }
}

.academic-search-button {
  background: linear-gradient(135deg, #e4f0ff 0%, #cfe3fe 100%);
  color: #337ed3;

  &:hover {
    background: linear-gradient(135deg, #d6e8ff 0%, #c1d9fe 100%);
  }
}

.academic-format-button {
  background: linear-gradient(135deg, #fff8e3 0%, #fae1b8 100%);
  color: #d08529;

  &:hover {
    background: linear-gradient(135deg, #fff2d6 0%, #f5d7a8 100%);
  }
}
</style>
